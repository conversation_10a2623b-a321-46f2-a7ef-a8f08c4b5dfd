package ch.eisenring.gui.util;

import java.awt.FontMetrics;
import java.awt.Graphics2D;
import java.awt.Rectangle;
import java.awt.Shape;

import javax.swing.SwingConstants;

public abstract class RenderUtil {

	protected RenderUtil() {
	}

	/**
	 * Renders the given text in a rectangular block.
	 * The text is clipped at the blocks boundaries and placed 
	 * according to the given JUSTFY_* mode.
	 */
	public static void text(final Graphics2D g, final String text,
			                int x, int y, int w, int h, int justification) {
		if (null==g || null==text || text.length()<1)
			return;
		final FontMetrics fm = g.getFontMetrics();
		final int tw = fm.stringWidth(text);
		final int th = fm.getHeight();
		final int tx, ty;
		// determine text location
		ty = y + ((h + th) >> 1) - fm.getDescent();
		switch (justification) {
			case SwingConstants.CENTER:
				tx = x + ((w - tw) >> 1);
				break;
			case SwingConstants.RIGHT:
			case SwingConstants.EAST:
				tx = x + w - 2 - tw;
				break;
			case SwingConstants.LEFT:
			case SwingConstants.WEST:
			default:
				tx = x + 2;
				break;
		}
		final Shape clip = g.getClip();
		if (clip != null) {
			final Rectangle clipRect = clip.getBounds();
			final Rectangle textRect = new Rectangle(x, y, w, h);
			final Rectangle intersection = textRect.intersection(clipRect);
			g.setClip(intersection);
		} else {
			g.setClip(x, y, w, h);
		}
		g.drawString(text, tx, ty);
		g.setClip(clip);
	}

}
