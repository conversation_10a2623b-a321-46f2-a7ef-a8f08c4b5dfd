package ch.eisenring.gui;

/**
 * Helper class for manual data binding
 */
public abstract class ManualBinding extends ValueEditorBindingBase {

	private final int length;
	
	protected ManualBinding() {
		this(1);
	}

	protected ManualBinding(final int length) {
		this.length = length;
	}

	// --------------------------------------------------------------
	// ---
	// --- ValueEditorBinding implementation
	// ---
	// --------------------------------------------------------------
	@Override
	public final int getLength() {
		return length;
	}

}
