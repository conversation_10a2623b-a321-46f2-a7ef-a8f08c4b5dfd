<?xml version="1.0" encoding="UTF-8"  ?>
<!-- Created with iReport - A designer for JasperReports -->
<!DOCTYPE jasperReport PUBLIC "//JasperReports//DTD Report Design//EN" "http://jasperreports.sourceforge.net/dtds/jasperreport.dtd">
<jasperReport
		 name="MBPAbschlussSeite"
		 columnCount="1"
		 printOrder="Vertical"
		 orientation="Portrait"
		 pageWidth="595"
		 pageHeight="842"
		 columnWidth="535"
		 columnSpacing="0"
		 leftMargin="30"
		 rightMargin="30"
		 topMargin="20"
		 bottomMargin="20"
		 whenNoDataType="NoPages"
		 isTitleNewPage="false"
		 isSummaryNewPage="false">
	<property name="ireport.scriptlethandling" value="0" />
	<property name="ireport.encoding" value="UTF-8" />
	<import value="java.util.*" />
	<import value="net.sf.jasperreports.engine.*" />
	<import value="net.sf.jasperreports.engine.data.*" />

	<parameter name="Projectnumber" isForPrompting="false" class="java.lang.String"/>
	<parameter name="PrintTimestamp" isForPrompting="false" class="java.lang.String"/>

	<field name="DocumentName" class="java.lang.String"/>
	<field name="DocumentType" class="java.lang.String"/>
	<field name="DocumentStatus" class="java.lang.String"/>

		<background>
			<band height="0"  isSplitAllowed="true" >
			</band>
		</background>
		<title>
			<band height="0"  isSplitAllowed="true" >
			</band>
		</title>
		<pageHeader>
			<band height="24"  isSplitAllowed="false" >
				<rectangle>
					<reportElement
						mode="Opaque"
						x="0"
						y="0"
						width="534"
						height="23"
						backcolor="#CCCCCC"
						key="rectangle-1"
						isPrintWhenDetailOverflows="true"/>
					<graphicElement stretchType="NoStretch"/>
				</rectangle>
				<textField isStretchWithOverflow="false" isBlankWhenNull="false" evaluationTime="Now" hyperlinkType="None"  hyperlinkTarget="Self" >
					<reportElement
						x="4"
						y="0"
						width="529"
						height="23"
						key="Title"/>
					<box></box>
					<textElement>
						<font pdfFontName="Helvetica-Bold" size="14" isBold="true"/>
					</textElement>
				<textFieldExpression   class="java.lang.String"><![CDATA["Druckreport " + $P{Projectnumber}]]></textFieldExpression>
				</textField>
				<textField isStretchWithOverflow="false" isBlankWhenNull="false" evaluationTime="Now" hyperlinkType="None"  hyperlinkTarget="Self" >
					<reportElement
						x="2"
						y="1"
						width="529"
						height="15"
						key="Title-1"/>
					<box></box>
					<textElement textAlignment="Right">
						<font pdfFontName="Helvetica-BoldOblique" size="8" isBold="true" isItalic="true"/>
					</textElement>
				<textFieldExpression   class="java.lang.String"><![CDATA[$P{PrintTimestamp}]]></textFieldExpression>
				</textField>
			</band>
		</pageHeader>
		<columnHeader>
			<band height="20"  isSplitAllowed="false" >
				<rectangle>
					<reportElement
						mode="Opaque"
						x="0"
						y="0"
						width="534"
						height="19"
						backcolor="#CCCCFF"
						key="rectangle-2"
						stretchType="RelativeToBandHeight"
						isPrintWhenDetailOverflows="true"/>
					<graphicElement stretchType="RelativeToBandHeight"/>
				</rectangle>
				<staticText>
					<reportElement
						x="2"
						y="0"
						width="178"
						height="19"
						key="T_DocumentName"/>
					<box></box>
					<textElement verticalAlignment="Middle">
						<font size="10"/>
					</textElement>
				<text><![CDATA[Dokument:]]></text>
				</staticText>
				<staticText>
					<reportElement
						x="180"
						y="0"
						width="178"
						height="19"
						key="T_DocumentType"/>
					<box></box>
					<textElement verticalAlignment="Middle">
						<font/>
					</textElement>
				<text><![CDATA[Art:]]></text>
				</staticText>
				<staticText>
					<reportElement
						x="356"
						y="0"
						width="178"
						height="19"
						key="T_DocumentStatus"/>
					<box></box>
					<textElement textAlignment="Right" verticalAlignment="Middle">
						<font/>
					</textElement>
				<text><![CDATA[Status:]]></text>
				</staticText>
			</band>
		</columnHeader>
		<detail>
			<band height="13"  isSplitAllowed="false" >
				<rectangle>
					<reportElement
						mode="Transparent"
						x="0"
						y="0"
						width="534"
						height="12"
						key="rectangle-3"
						stretchType="RelativeToBandHeight"
						isPrintWhenDetailOverflows="true"/>
					<graphicElement stretchType="RelativeToBandHeight">
					<pen lineWidth="0.33" lineStyle="Solid"/>
</graphicElement>
				</rectangle>
				<textField isStretchWithOverflow="true" isBlankWhenNull="false" evaluationTime="Now" hyperlinkType="None"  hyperlinkTarget="Self" >
					<reportElement
						x="2"
						y="0"
						width="178"
						height="13"
						key="F_DocumentName"
						stretchType="RelativeToTallestObject"
						isPrintWhenDetailOverflows="true"/>
					<box></box>
					<textElement verticalAlignment="Middle">
						<font size="9"/>
					</textElement>
				<textFieldExpression   class="java.lang.String"><![CDATA[$F{DocumentName}]]></textFieldExpression>
				</textField>
				<textField isStretchWithOverflow="true" isBlankWhenNull="false" evaluationTime="Now" hyperlinkType="None"  hyperlinkTarget="Self" >
					<reportElement
						x="180"
						y="0"
						width="178"
						height="13"
						key="F_DocumentType"
						stretchType="RelativeToTallestObject"
						isPrintWhenDetailOverflows="true"/>
					<box></box>
					<textElement verticalAlignment="Middle">
						<font size="9"/>
					</textElement>
				<textFieldExpression   class="java.lang.String"><![CDATA[$F{DocumentType}]]></textFieldExpression>
				</textField>
				<textField isStretchWithOverflow="true" isBlankWhenNull="false" evaluationTime="Now" hyperlinkType="None"  hyperlinkTarget="Self" >
					<reportElement
						x="356"
						y="0"
						width="178"
						height="12"
						key="F_DocumentStatus"
						stretchType="RelativeToTallestObject"
						isPrintWhenDetailOverflows="true"/>
					<box></box>
					<textElement textAlignment="Right" verticalAlignment="Middle">
						<font size="9"/>
					</textElement>
				<textFieldExpression   class="java.lang.String"><![CDATA[$F{DocumentStatus}]]></textFieldExpression>
				</textField>
			</band>
		</detail>
		<columnFooter>
			<band height="0"  isSplitAllowed="true" >
			</band>
		</columnFooter>
		<pageFooter>
			<band height="0"  isSplitAllowed="true" >
			</band>
		</pageFooter>
		<summary>
			<band height="0"  isSplitAllowed="true" >
			</band>
		</summary>
</jasperReport>
