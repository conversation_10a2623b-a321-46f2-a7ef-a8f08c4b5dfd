<?xml version="1.0" encoding="UTF-8"?>
<jasperReport xmlns="http://jasperreports.sourceforge.net/jasperreports" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://jasperreports.sourceforge.net/jasperreports http://jasperreports.sourceforge.net/xsd/jasperreport.xsd" name="MonteurWochenAuftragListe" pageWidth="595" pageHeight="842" columnWidth="535" leftMargin="30" rightMargin="30" topMargin="20" bottomMargin="20" uuid="30acf030-ff55-4df5-8b9a-0b555cafae85">
	<property name="ireport.scriptlethandling" value="0"/>
	<property name="ireport.encoding" value="UTF-8"/>
	<property name="ireport.zoom" value="1.0"/>
	<property name="ireport.x" value="0"/>
	<property name="ireport.y" value="0"/>
	<import value="net.sf.jasperreports.engine.*"/>
	<import value="java.util.*"/>
	<import value="net.sf.jasperreports.engine.data.*"/>
	<parameter name="NAME" class="java.lang.String" isForPrompting="false">
		<parameterDescription><![CDATA[Name des Monteurs]]></parameterDescription>
		<defaultValueExpression><![CDATA["Test Name"]]></defaultValueExpression>
	</parameter>
	<parameter name="WEEK" class="java.lang.String" isForPrompting="false">
		<parameterDescription><![CDATA[Kalenderwoche]]></parameterDescription>
		<defaultValueExpression><![CDATA["36"]]></defaultValueExpression>
	</parameter>
	<parameter name="YEAR" class="java.lang.String" isForPrompting="false">
		<parameterDescription><![CDATA[Kalenderjahr]]></parameterDescription>
		<defaultValueExpression><![CDATA["2008"]]></defaultValueExpression>
	</parameter>
	<parameter name="DRUCKDATUM" class="java.lang.String" isForPrompting="false"/>
	<parameter name="FARBMARKIERUNG" class="java.lang.Integer" isForPrompting="false"/>
	<field name="title" class="java.lang.String">
		<fieldDescription><![CDATA[Weekday + Date]]></fieldDescription>
	</field>
	<field name="objektText" class="java.lang.String">
		<fieldDescription><![CDATA[Objekt Text]]></fieldDescription>
	</field>
	<field name="projektNummer" class="java.lang.String">
		<fieldDescription><![CDATA[Kom-Nummer(n)]]></fieldDescription>
	</field>
	<field name="arbeitenText" class="java.lang.String">
		<fieldDescription><![CDATA[Zu erledigende Arbeit(en)]]></fieldDescription>
	</field>
	<field name="wochenTag" class="java.lang.String">
		<fieldDescription><![CDATA[Wochentag (Zeile)]]></fieldDescription>
	</field>
	<field name="datum" class="java.lang.String">
		<fieldDescription><![CDATA[Datum (Zeile)]]></fieldDescription>
	</field>
	<background>
		<band splitType="Stretch"/>
	</background>
	<title>
		<band splitType="Stretch"/>
	</title>
	<pageHeader>
		<band height="50" splitType="Prevent">
			<rectangle>
				<reportElement uuid="425ad139-4215-4e25-9898-776b6e5eae7c" key="rectangle-1" mode="Opaque" x="0" y="0" width="534" height="50" isPrintWhenDetailOverflows="true" backcolor="#CCCCCC"/>
			</rectangle>
			<rectangle radius="2">
				<reportElement uuid="05d3b67b-a242-4030-bf0e-aa2a20fa2628" key="Marker_01_Black" x="365" y="21" width="21" height="21" backcolor="#000000">
					<printWhenExpression><![CDATA[Boolean.valueOf(
Integer.valueOf(1).equals(
$P{FARBMARKIERUNG}
))]]></printWhenExpression>
				</reportElement>
			</rectangle>
			<rectangle radius="2">
				<reportElement uuid="e71293ab-b7ed-4979-87ba-d86b847227bf" key="Marker_02_Blue" x="365" y="21" width="21" height="21" backcolor="#0000FF">
					<printWhenExpression><![CDATA[Boolean.valueOf(
Integer.valueOf(2).equals(
$P{FARBMARKIERUNG}
))]]></printWhenExpression>
				</reportElement>
			</rectangle>
			<rectangle radius="2">
				<reportElement uuid="28404489-f73e-4e5f-8938-6ebf921c4372" key="Marker_03_Gray" x="365" y="21" width="21" height="21" backcolor="#999999">
					<printWhenExpression><![CDATA[Boolean.valueOf(
Integer.valueOf(3).equals(
$P{FARBMARKIERUNG}
))]]></printWhenExpression>
				</reportElement>
			</rectangle>
			<rectangle radius="2">
				<reportElement uuid="c503d793-a272-4162-8dc2-e108a27117ec" key="Marker_04_Green" x="365" y="21" width="21" height="21" backcolor="#00CC00">
					<printWhenExpression><![CDATA[Boolean.valueOf(
Integer.valueOf(4).equals(
$P{FARBMARKIERUNG}
))]]></printWhenExpression>
				</reportElement>
			</rectangle>
			<rectangle radius="2">
				<reportElement uuid="6c43d40f-6b01-4e96-bbeb-783f2244e6fd" key="Marker_05_Orange" x="365" y="21" width="21" height="21" backcolor="#FF9900">
					<printWhenExpression><![CDATA[Boolean.valueOf(
Integer.valueOf(5).equals(
$P{FARBMARKIERUNG}
))]]></printWhenExpression>
				</reportElement>
			</rectangle>
			<rectangle radius="2">
				<reportElement uuid="a8e1e80d-9faf-4e9b-8ef8-1ac27c595443" key="Marker_06_Purple" x="365" y="21" width="21" height="21" backcolor="#CC00CC">
					<printWhenExpression><![CDATA[Boolean.valueOf(
Integer.valueOf(6).equals(
$P{FARBMARKIERUNG}
))]]></printWhenExpression>
				</reportElement>
			</rectangle>
			<rectangle radius="2">
				<reportElement uuid="39910367-6aeb-4c2e-aed8-fe37cacdc661" key="Marker_07_Red" x="365" y="21" width="21" height="21" backcolor="#FF0000">
					<printWhenExpression><![CDATA[Boolean.valueOf(
Integer.valueOf(7).equals(
$P{FARBMARKIERUNG}
))]]></printWhenExpression>
				</reportElement>
			</rectangle>
			<rectangle radius="2">
				<reportElement uuid="4e470f58-c265-4cd4-bb3b-7aa4ac9b2efa" key="Marker_08_Teal" x="365" y="21" width="21" height="21" backcolor="#00FFFF">
					<printWhenExpression><![CDATA[Boolean.valueOf(
Integer.valueOf(8).equals(
$P{FARBMARKIERUNG}
))]]></printWhenExpression>
				</reportElement>
			</rectangle>
			<rectangle radius="2">
				<reportElement uuid="368c6c7a-bf84-406e-aece-84f213e49f42" key="Marker_09_White" x="365" y="21" width="21" height="21" backcolor="#FFFFFF">
					<printWhenExpression><![CDATA[Boolean.valueOf(
Integer.valueOf(9).equals(
$P{FARBMARKIERUNG}
))]]></printWhenExpression>
				</reportElement>
			</rectangle>
			<rectangle radius="2">
				<reportElement uuid="3d3c838d-d85b-4cd0-98fd-a10eccd5d819" key="Marker_0A_Yellow" x="365" y="21" width="21" height="21" backcolor="#FFFF00">
					<printWhenExpression><![CDATA[Boolean.valueOf(
Integer.valueOf(10).equals(
$P{FARBMARKIERUNG}
))]]></printWhenExpression>
				</reportElement>
			</rectangle>
			<staticText>
				<reportElement uuid="9e00bdea-81aa-4da8-826c-d5331638cd81" key="Label_Name" x="5" y="0" width="360" height="14"/>
				<textElement>
					<font fontName="Avenir LT 35 Light" isUnderline="false" isPdfEmbedded="true"/>
				</textElement>
				<text><![CDATA[Name:]]></text>
			</staticText>
			<staticText>
				<reportElement uuid="051d06a3-1272-4304-aa5a-ce98c1deec15" key="Label_Woche" x="390" y="0" width="70" height="14"/>
				<textElement>
					<font fontName="Avenir LT 35 Light" isPdfEmbedded="true"/>
				</textElement>
				<text><![CDATA[Woche:]]></text>
			</staticText>
			<staticText>
				<reportElement uuid="d5b21528-f79d-4b25-80bf-1af66527d42a" key="Label_Jahr" x="460" y="0" width="70" height="14"/>
				<textElement>
					<font fontName="Avenir LT 35 Light" isPdfEmbedded="true"/>
				</textElement>
				<text><![CDATA[Jahr:]]></text>
			</staticText>
			<textField isBlankWhenNull="false">
				<reportElement uuid="4067d671-6788-49bb-b98c-5f39e1328e88" key="Name" x="5" y="17" width="360" height="29"/>
				<textElement>
					<font fontName="Avenir LT 35 Light" size="20" isBold="true" pdfFontName="Helvetica-Bold" isPdfEmbedded="true"/>
				</textElement>
				<textFieldExpression><![CDATA[$P{NAME}]]></textFieldExpression>
			</textField>
			<textField isBlankWhenNull="false">
				<reportElement uuid="29885f5b-c5e4-4cc7-a762-e1209b4fdc8e" key="Woche" x="390" y="17" width="70" height="29"/>
				<textElement>
					<font fontName="Avenir LT 35 Light" size="20" isBold="true" pdfFontName="Helvetica-Bold" isPdfEmbedded="true"/>
				</textElement>
				<textFieldExpression><![CDATA[$P{WEEK}]]></textFieldExpression>
			</textField>
			<textField isBlankWhenNull="false">
				<reportElement uuid="27ca1533-ec12-47b4-9840-3e9f9698a19c" key="Jahr" x="460" y="17" width="70" height="29"/>
				<textElement>
					<font fontName="Avenir LT 35 Light" size="20" isBold="true" pdfFontName="Helvetica-Bold" isPdfEmbedded="true"/>
				</textElement>
				<textFieldExpression><![CDATA[$P{YEAR}]]></textFieldExpression>
			</textField>
		</band>
	</pageHeader>
	<columnHeader>
		<band height="20" splitType="Prevent">
			<rectangle>
				<reportElement uuid="b3642302-a9b3-4526-89ab-c30a2e5edb05" key="rectangle-2" stretchType="RelativeToBandHeight" mode="Opaque" x="0" y="0" width="534" height="19" isPrintWhenDetailOverflows="true" backcolor="#CCCCFF"/>
			</rectangle>
			<staticText>
				<reportElement uuid="55ceda3f-111e-4b4f-a597-7bee3a6bc8c3" key="staticText-1" x="4" y="0" width="61" height="19"/>
				<textElement verticalAlignment="Middle">
					<font fontName="Avenir LT 35 Light" isBold="true" pdfFontName="Helvetica-Bold" isPdfEmbedded="true"/>
				</textElement>
				<text><![CDATA[Wochentag]]></text>
			</staticText>
			<staticText>
				<reportElement uuid="5caa2cb1-8c30-4097-9f9c-f2e84c78ca1d" key="staticText-2" x="65" y="0" width="187" height="19"/>
				<textElement verticalAlignment="Middle">
					<font fontName="Avenir LT 35 Light" isBold="true" pdfFontName="Helvetica-Bold" isPdfEmbedded="true"/>
				</textElement>
				<text><![CDATA[Objekt]]></text>
			</staticText>
			<staticText>
				<reportElement uuid="95ef054a-2ccb-4473-ac9e-78ad242f01b3" key="staticText-3" x="252" y="0" width="76" height="19"/>
				<textElement verticalAlignment="Middle">
					<font fontName="Avenir LT 35 Light" isBold="true" pdfFontName="Helvetica-Bold" isPdfEmbedded="true"/>
				</textElement>
				<text><![CDATA[Kom-Nummer]]></text>
			</staticText>
			<staticText>
				<reportElement uuid="db25b12b-92df-4ff9-8d36-b73984ef5214" key="staticText-4" x="328" y="0" width="207" height="19"/>
				<textElement verticalAlignment="Middle">
					<font fontName="Avenir LT 35 Light" isBold="true" pdfFontName="Helvetica-Bold" isPdfEmbedded="true"/>
				</textElement>
				<text><![CDATA[Arbeiten]]></text>
			</staticText>
			<textField isStretchWithOverflow="true" pattern="" isBlankWhenNull="true">
				<reportElement uuid="d28dc463-e6a8-4338-b29b-00bdbab74b71" key="textField-9" mode="Opaque" x="424" y="1" width="109" height="9" forecolor="#000066" backcolor="#9999FF"/>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Avenir LT 35 Light" size="7" isBold="true" isItalic="true" pdfFontName="Helvetica-BoldOblique" isPdfEmbedded="true"/>
				</textElement>
				<textFieldExpression><![CDATA["Gedruckt am: "+$P{DRUCKDATUM}]]></textFieldExpression>
			</textField>
		</band>
	</columnHeader>
	<detail>
		<band height="118" splitType="Prevent">
			<rectangle>
				<reportElement uuid="803f6617-6a81-42ac-9dd1-e8adaea8fa44" key="rectangle-3" stretchType="RelativeToBandHeight" mode="Transparent" x="0" y="0" width="534" height="118" isPrintWhenDetailOverflows="true"/>
			</rectangle>
			<textField isBlankWhenNull="false">
				<reportElement uuid="452b2a38-48c1-4c4b-a96e-17d2459fd42e" key="textField-1" x="4" y="0" width="61" height="14" isPrintWhenDetailOverflows="true"/>
				<textElement>
					<font fontName="Avenir LT 35 Light" isPdfEmbedded="true"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{wochenTag}]]></textFieldExpression>
			</textField>
			<textField isStretchWithOverflow="true" isBlankWhenNull="false">
				<reportElement uuid="51dcf6bc-f51f-4de7-8145-3ac8bdb454f0" key="textField-2" stretchType="RelativeToTallestObject" x="65" y="0" width="187" height="118" isPrintWhenDetailOverflows="true"/>
				<textElement>
					<font fontName="Avenir LT 35 Light" isPdfEmbedded="true"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{objektText}]]></textFieldExpression>
			</textField>
			<textField isStretchWithOverflow="true" isBlankWhenNull="false">
				<reportElement uuid="e84ab813-8146-49f9-b83b-4590f9b5b5e7" key="textField-3" stretchType="RelativeToTallestObject" x="252" y="0" width="76" height="118" isPrintWhenDetailOverflows="true"/>
				<textElement>
					<font fontName="Avenir LT 35 Light" isPdfEmbedded="true"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{projektNummer}]]></textFieldExpression>
			</textField>
			<textField isStretchWithOverflow="true" isBlankWhenNull="false">
				<reportElement uuid="18d8b7e2-5be4-498b-ac2d-757a709750ac" key="textField-4" stretchType="RelativeToTallestObject" x="328" y="0" width="207" height="118" isPrintWhenDetailOverflows="true"/>
				<textElement>
					<font fontName="Avenir LT 35 Light" isPdfEmbedded="true"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{arbeitenText}]]></textFieldExpression>
			</textField>
			<textField isBlankWhenNull="false">
				<reportElement uuid="20aee614-36ce-4b31-8064-73377c2a02c4" key="textField-8" x="4" y="14" width="61" height="14"/>
				<textElement>
					<font fontName="Avenir LT 35 Light" isPdfEmbedded="true"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{datum}]]></textFieldExpression>
			</textField>
		</band>
	</detail>
	<columnFooter>
		<band splitType="Stretch"/>
	</columnFooter>
	<pageFooter>
		<band splitType="Stretch"/>
	</pageFooter>
	<summary>
		<band splitType="Stretch"/>
	</summary>
</jasperReport>
