<?xml version="1.0" encoding="UTF-8"?>
<jasperReport xmlns="http://jasperreports.sourceforge.net/jasperreports" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://jasperreports.sourceforge.net/jasperreports http://jasperreports.sourceforge.net/xsd/jasperreport.xsd" name="MonteurTagAuftragListe" pageWidth="595" pageHeight="842" columnWidth="535" leftMargin="30" rightMargin="30" topMargin="20" bottomMargin="20" uuid="0a2e716e-c6bb-41ae-8312-3dc4e5868f0a">
	<property name="ireport.scriptlethandling" value="0"/>
	<property name="ireport.encoding" value="UTF-8"/>
	<property name="ireport.zoom" value="1.0"/>
	<property name="ireport.x" value="0"/>
	<property name="ireport.y" value="0"/>
	<import value="net.sf.jasperreports.engine.*"/>
	<import value="java.util.*"/>
	<import value="net.sf.jasperreports.engine.data.*"/>
	<parameter name="NAME" class="java.lang.String" isForPrompting="false">
		<parameterDescription><![CDATA[Name des Monteurs]]></parameterDescription>
		<defaultValueExpression><![CDATA["Test Name"]]></defaultValueExpression>
	</parameter>
	<parameter name="WEEK" class="java.lang.String" isForPrompting="false">
		<parameterDescription><![CDATA[Kalenderwoche]]></parameterDescription>
		<defaultValueExpression><![CDATA["36"]]></defaultValueExpression>
	</parameter>
	<parameter name="YEAR" class="java.lang.String" isForPrompting="false">
		<parameterDescription><![CDATA[Kalenderjahr]]></parameterDescription>
		<defaultValueExpression><![CDATA["2008"]]></defaultValueExpression>
	</parameter>
	<parameter name="WEEKDAY" class="java.lang.String" isForPrompting="false"/>
	<parameter name="FAHRZEUG" class="java.lang.String" isForPrompting="false"/>
	<parameter name="DRUCKDATUM" class="java.lang.String" isForPrompting="false"/>
	<parameter name="FARBMARKIERUNG" class="java.lang.Integer" isForPrompting="false"/>
	<field name="objektText" class="java.lang.String">
		<fieldDescription><![CDATA[Objekt Text]]></fieldDescription>
	</field>
	<field name="projektNummer" class="java.lang.String">
		<fieldDescription><![CDATA[Kom-Nummer(n)]]></fieldDescription>
	</field>
	<field name="arbeitenText" class="java.lang.String">
		<fieldDescription><![CDATA[Zu erledigende Arbeit(en)]]></fieldDescription>
	</field>
	<background>
		<band splitType="Stretch"/>
	</background>
	<title>
		<band splitType="Stretch"/>
	</title>
	<pageHeader>
		<band height="61" splitType="Prevent">
			<rectangle>
				<reportElement uuid="e5c3a2d7-3a9a-4055-9fe1-43b9c3f15fc2" key="rectangle-4" stretchType="RelativeToBandHeight" mode="Opaque" x="0" y="50" width="530" height="11" isPrintWhenDetailOverflows="true" backcolor="#CCCCCC"/>
			</rectangle>
			<rectangle>
				<reportElement uuid="960c8c8b-deeb-45f6-b574-dbe49e1f267d" key="rectangle-1" mode="Opaque" x="0" y="0" width="530" height="50" isPrintWhenDetailOverflows="true" backcolor="#CCCCCC"/>
			</rectangle>
			<rectangle radius="2">
				<reportElement uuid="0c183763-46a1-4d64-adef-9eeba811bb4d" key="Marker_01_Black" x="295" y="21" width="21" height="21" backcolor="#000000">
					<printWhenExpression><![CDATA[Boolean.valueOf(
Integer.valueOf(1).equals(
$P{FARBMARKIERUNG}
))]]></printWhenExpression>
				</reportElement>
			</rectangle>
			<rectangle radius="2">
				<reportElement uuid="bfb17b02-7b5d-45a2-a0a8-04710fce86c2" key="Marker_02_Blue" x="295" y="21" width="21" height="21" backcolor="#0000FF">
					<printWhenExpression><![CDATA[Boolean.valueOf(
Integer.valueOf(2).equals(
$P{FARBMARKIERUNG}
))]]></printWhenExpression>
				</reportElement>
			</rectangle>
			<rectangle radius="2">
				<reportElement uuid="a4effb9c-11f0-4829-bc8d-3a6d02b3a524" key="Marker_03_Gray" x="295" y="21" width="21" height="21" backcolor="#999999">
					<printWhenExpression><![CDATA[Boolean.valueOf(
Integer.valueOf(3).equals(
$P{FARBMARKIERUNG}
))]]></printWhenExpression>
				</reportElement>
			</rectangle>
			<rectangle radius="2">
				<reportElement uuid="0850dd8e-3c3c-4ee8-8504-9c95a797e00a" key="Marker_04_Green" x="295" y="21" width="21" height="21" backcolor="#00CC00">
					<printWhenExpression><![CDATA[Boolean.valueOf(
Integer.valueOf(4).equals(
$P{FARBMARKIERUNG}
))]]></printWhenExpression>
				</reportElement>
			</rectangle>
			<rectangle radius="2">
				<reportElement uuid="ceb6d3f0-01f8-4a06-a655-4283d72be294" key="Marker_05_Orange" x="295" y="21" width="21" height="21" backcolor="#FF9900">
					<printWhenExpression><![CDATA[Boolean.valueOf(
Integer.valueOf(5).equals(
$P{FARBMARKIERUNG}
))]]></printWhenExpression>
				</reportElement>
			</rectangle>
			<rectangle radius="2">
				<reportElement uuid="9db53af3-b5a2-42aa-ac73-c024f7e5026b" key="Marker_06_Purple" x="295" y="21" width="21" height="21" backcolor="#CC00CC">
					<printWhenExpression><![CDATA[Boolean.valueOf(
Integer.valueOf(6).equals(
$P{FARBMARKIERUNG}
))]]></printWhenExpression>
				</reportElement>
			</rectangle>
			<rectangle radius="2">
				<reportElement uuid="3123b19d-0af8-4801-867d-f904a9a8a422" key="Marker_07_Red" x="295" y="21" width="21" height="21" backcolor="#FF0033">
					<printWhenExpression><![CDATA[Boolean.valueOf(
Integer.valueOf(7).equals(
$P{FARBMARKIERUNG}
))]]></printWhenExpression>
				</reportElement>
			</rectangle>
			<rectangle radius="2">
				<reportElement uuid="fa48bb63-8608-4b8d-b401-afe728bf97b4" key="Marker_08_Teal" x="295" y="21" width="21" height="21" backcolor="#00FFFF">
					<printWhenExpression><![CDATA[Boolean.valueOf(
Integer.valueOf(8).equals(
$P{FARBMARKIERUNG}
))]]></printWhenExpression>
				</reportElement>
			</rectangle>
			<rectangle radius="2">
				<reportElement uuid="b74ba5c4-f85a-4a5e-ad75-2fb1a9d3e2e4" key="Marker_09_White" x="295" y="21" width="21" height="21" forecolor="#000000" backcolor="#FFFFFF">
					<printWhenExpression><![CDATA[Boolean.valueOf(
Integer.valueOf(9).equals(
$P{FARBMARKIERUNG}
))]]></printWhenExpression>
				</reportElement>
			</rectangle>
			<rectangle radius="2">
				<reportElement uuid="15abebb4-f3c6-4866-a249-3f1bfcf4250d" key="Marker_0A_Yellow" x="295" y="21" width="21" height="21" backcolor="#FFFF00">
					<printWhenExpression><![CDATA[Boolean.valueOf(
Integer.valueOf(10).equals(
$P{FARBMARKIERUNG}
))]]></printWhenExpression>
				</reportElement>
			</rectangle>
			<staticText>
				<reportElement uuid="88668479-4fe6-4450-ac92-54fa1b040569" key="Label_Name" x="4" y="0" width="315" height="14"/>
				<textElement verticalAlignment="Top">
					<font fontName="Avenir LT 35 Light" isUnderline="false" isPdfEmbedded="true"/>
				</textElement>
				<text><![CDATA[Name:]]></text>
			</staticText>
			<staticText>
				<reportElement uuid="7af8ff93-adf1-419d-9bc7-755dcc63c251" key="Label_Tag" x="320" y="0" width="110" height="14"/>
				<textElement>
					<font fontName="Avenir LT 35 Light" isPdfEmbedded="true"/>
				</textElement>
				<text><![CDATA[Tag:]]></text>
			</staticText>
			<staticText>
				<reportElement uuid="2975c99e-5131-42c1-b3f5-5627f83ba59a" key="Label_Woche" x="423" y="0" width="58" height="14"/>
				<textElement>
					<font fontName="Avenir LT 35 Light" isPdfEmbedded="true"/>
				</textElement>
				<text><![CDATA[Woche:]]></text>
			</staticText>
			<staticText>
				<reportElement uuid="475de62e-179e-4ce6-8b20-96fac2b53ce5" key="Label_Fahrzeug" x="4" y="50" width="88" height="11"/>
				<textElement verticalAlignment="Middle">
					<font fontName="Avenir LT 35 Light" size="7" isBold="true" pdfFontName="Helvetica-Bold" isPdfEmbedded="true"/>
				</textElement>
				<text><![CDATA[Fahrzeug / Anhänger]]></text>
			</staticText>
			<staticText>
				<reportElement uuid="f5289ca8-1f3e-480b-beab-7820eca97776" key="Label_Jahr" x="477" y="0" width="58" height="14"/>
				<textElement>
					<font fontName="Avenir LT 35 Light" isPdfEmbedded="true"/>
				</textElement>
				<text><![CDATA[Jahr:]]></text>
			</staticText>
			<textField isBlankWhenNull="false">
				<reportElement uuid="8fa9aa39-232f-42bc-8804-8fbf36706a2d" key="Name" x="4" y="17" width="315" height="29"/>
				<textElement>
					<font fontName="Avenir LT 35 Light" size="20" isBold="true" pdfFontName="Helvetica-Bold" isPdfEmbedded="true"/>
				</textElement>
				<textFieldExpression><![CDATA[$P{NAME}]]></textFieldExpression>
			</textField>
			<textField isBlankWhenNull="false">
				<reportElement uuid="fdab5150-c9ec-4db0-b777-77b91207af0a" key="Tag" x="320" y="17" width="110" height="29"/>
				<textElement>
					<font fontName="Avenir LT 35 Light" size="20" isBold="true" pdfFontName="Helvetica-Bold" isPdfEmbedded="true"/>
				</textElement>
				<textFieldExpression><![CDATA[$P{WEEKDAY}]]></textFieldExpression>
			</textField>
			<textField isBlankWhenNull="false">
				<reportElement uuid="882b4071-1543-44ba-a527-30a89497d0bc" key="Woche" x="423" y="17" width="58" height="29"/>
				<textElement>
					<font fontName="Avenir LT 35 Light" size="20" isBold="true" pdfFontName="Helvetica-Bold" isPdfEmbedded="true"/>
				</textElement>
				<textFieldExpression><![CDATA[$P{WEEK}]]></textFieldExpression>
			</textField>
			<textField isBlankWhenNull="false">
				<reportElement uuid="e4d9a33b-8e71-49a9-a7c6-719118d6862a" key="Jahr" x="477" y="17" width="58" height="29"/>
				<textElement>
					<font fontName="Avenir LT 35 Light" size="20" isBold="true" pdfFontName="Helvetica-Bold" isPdfEmbedded="true"/>
				</textElement>
				<textFieldExpression><![CDATA[$P{YEAR}]]></textFieldExpression>
			</textField>
			<textField isStretchWithOverflow="true" pattern="" isBlankWhenNull="true">
				<reportElement uuid="41ce294e-94b2-4661-8053-262c9e33d3bf" key="Fahrzeug" x="92" y="50" width="268" height="11"/>
				<textElement verticalAlignment="Middle">
					<font fontName="Avenir LT 35 Light" size="7" isBold="true" pdfFontName="Helvetica-Bold" isPdfEmbedded="true"/>
				</textElement>
				<textFieldExpression><![CDATA[$P{FAHRZEUG}]]></textFieldExpression>
			</textField>
			<textField isStretchWithOverflow="true" pattern="" isBlankWhenNull="true">
				<reportElement uuid="b2a0a37f-925c-4252-a2ad-884b42dbbcfe" key="GedrucktAm" mode="Opaque" x="420" y="51" width="109" height="9" forecolor="#000066" backcolor="#999999">
					<printWhenExpression><![CDATA[Boolean.valueOf($P{DRUCKDATUM} != null)]]></printWhenExpression>
				</reportElement>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Avenir LT 35 Light" size="7" isBold="true" isItalic="true" pdfFontName="Helvetica-BoldOblique" isPdfEmbedded="true"/>
				</textElement>
				<textFieldExpression><![CDATA["Gedruckt am: "+$P{DRUCKDATUM}]]></textFieldExpression>
			</textField>
		</band>
	</pageHeader>
	<columnHeader>
		<band height="19" splitType="Prevent">
			<rectangle>
				<reportElement uuid="6c839cdc-57c6-494f-8af6-35ce6bfabab6" key="rectangle-2" stretchType="RelativeToBandHeight" mode="Opaque" x="0" y="0" width="530" height="19" isPrintWhenDetailOverflows="true" backcolor="#CCCCFF"/>
			</rectangle>
			<staticText>
				<reportElement uuid="96ba828f-6519-47ed-af11-1cbfd1180afd" key="staticText-2" x="4" y="0" width="240" height="19"/>
				<textElement textAlignment="Left" verticalAlignment="Middle">
					<font isBold="true" pdfFontName="Helvetica-Bold" isPdfEmbedded="true"/>
				</textElement>
				<text><![CDATA[Objekt]]></text>
			</staticText>
			<staticText>
				<reportElement uuid="d0bff70c-0cf7-445a-ab61-3e2267821a83" key="staticText-3" x="244" y="0" width="76" height="19"/>
				<textElement verticalAlignment="Middle">
					<font isBold="true" pdfFontName="Helvetica-Bold" isPdfEmbedded="true"/>
				</textElement>
				<text><![CDATA[Kom-Nummer]]></text>
			</staticText>
			<staticText>
				<reportElement uuid="1a832d19-417b-4d64-a7d5-35648bc4a744" key="staticText-4" x="320" y="0" width="210" height="19"/>
				<textElement verticalAlignment="Middle">
					<font isBold="true" pdfFontName="Helvetica-Bold" isPdfEmbedded="true"/>
				</textElement>
				<text><![CDATA[Arbeiten]]></text>
			</staticText>
		</band>
	</columnHeader>
	<detail>
		<band height="55" splitType="Prevent">
			<rectangle>
				<reportElement uuid="f7ad8299-aeab-44fe-8f80-d37838f6a487" key="rectangle-3" stretchType="RelativeToBandHeight" mode="Transparent" x="0" y="0" width="530" height="55" isPrintWhenDetailOverflows="true"/>
			</rectangle>
			<textField isStretchWithOverflow="true" isBlankWhenNull="false">
				<reportElement uuid="0ae0ac1b-9c5a-4390-a707-7210e51f459b" key="textField-2" stretchType="RelativeToTallestObject" x="4" y="0" width="240" height="55" isPrintWhenDetailOverflows="true"/>
				<textElement>
					<font fontName="Avenir LT 35 Light" isPdfEmbedded="true"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{objektText}]]></textFieldExpression>
			</textField>
			<textField isStretchWithOverflow="true" isBlankWhenNull="false">
				<reportElement uuid="139aa885-a573-4f9d-a2d3-c29f6e71f005" key="textField-3" stretchType="RelativeToTallestObject" x="244" y="0" width="76" height="55" isPrintWhenDetailOverflows="true"/>
				<textElement>
					<font fontName="Avenir LT 35 Light" isPdfEmbedded="true"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{projektNummer}]]></textFieldExpression>
			</textField>
			<textField isStretchWithOverflow="true" isBlankWhenNull="false">
				<reportElement uuid="2588d71f-213c-44cb-8523-9ef6cb5ba836" key="textField-4" stretchType="RelativeToTallestObject" x="320" y="0" width="210" height="55" isPrintWhenDetailOverflows="true"/>
				<textElement>
					<font fontName="Avenir LT 35 Light" isPdfEmbedded="true"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{arbeitenText}]]></textFieldExpression>
			</textField>
		</band>
	</detail>
	<columnFooter>
		<band splitType="Stretch"/>
	</columnFooter>
	<pageFooter>
		<band splitType="Stretch"/>
	</pageFooter>
	<summary>
		<band splitType="Stretch"/>
	</summary>
</jasperReport>
