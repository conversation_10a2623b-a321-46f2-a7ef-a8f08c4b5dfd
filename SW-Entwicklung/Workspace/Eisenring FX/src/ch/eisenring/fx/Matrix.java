package ch.eisenring.fx;

import java.awt.AlphaComposite;
import java.awt.Color;
import java.awt.Composite;
import java.awt.Font;
import java.awt.Graphics;
import java.awt.Graphics2D;
import java.awt.RenderingHints;

@SuppressWarnings("serial")
public class Matrix extends FullScreenFX {

	public static void main(String[] argv) {
		Matrix fx = new Matrix();
		try {
			Thread.sleep(1525);
		} catch (InterruptedException e) {
		}
		fx.enterFullscreenMode();
		while(!fx.inputDetected) {
			try {
				Thread.sleep(20);
			} catch (InterruptedException e) {
			}
			fx.repaint();
		}
		System.exit(0);
	}

	private final static int FONT_SIZE = 24;
	private final static char[] CHARS = {
		'0', '1', '2', '3', '4', '5', '6', '7',
		'8', '9', 'A', 'B', 'C', 'D', 'E', 'F',
		'G', 'H', 'I', 'J', 'K', 'L', 'M', 'N',
		'O', 'P', 'Q', 'R', 'S', 'T', 'U', 'V',
		'W', 'X', 'Y', 'Z', 'A', 'B', 'C', 'D',
		'E', 'F', 'G', 'A', 'Q', '0', 'W', 'Z',
		'X', 'P', 'A', '&', 'U', 'C', '£', 'ß',
		'X', 'L', 'Þ', 'K', '§', 'F', 'O', 'Ê'
	};
	
	private final static int RND_MULT = 0x8000;
	private final static int RND_CNTR = RND_MULT >> 1;
	private final static int VLIMIT = 0x24567;
	private final static int CSIZE = 47;
	private Color blobColor = new Color(0x1F000000, true);
	private Composite composite = AlphaComposite.getInstance(AlphaComposite.DST_OUT, 1F);
	private Font font;
	private int spot_x = 0;
	private int spot_y = 33 << 16;
	private int veloc_x = 0x7500;
	private int veloc_y = 0xC712;
	
	@Override
	public void paint(final Graphics g) {
		final Graphics2D g2d = backbuffer.createGraphics();
		g2d.setPaintMode();
		g2d.setColor(Color.BLACK);
		g2d.fillRect(0, 0, 32767, 32767);

		if (font == null) {
			font = new Font(Font.MONOSPACED, Font.BOLD, FONT_SIZE);
		}
		g2d.setFont(font);
		g2d.setRenderingHint(RenderingHints.KEY_TEXT_ANTIALIASING, RenderingHints.VALUE_TEXT_ANTIALIAS_ON);
		g2d.setColor(Color.GREEN);
		final int w = backbuffer.getWidth();
		final int h = backbuffer.getHeight();
		int tick = ((int) System.currentTimeMillis()) & 0xFFFF;
		int column = 0;
		int random = ((int) (Math.random() * 65536)) & 0xFFFF;
		final char[] character = new char[1];
		for (int x = -FONT_SIZE; x <= w; x += FONT_SIZE) {
			column += 1;
			int offset = (tick >> (column & 15)) % FONT_SIZE;
			for (int y = -FONT_SIZE; y <= h; y += FONT_SIZE) {
				random += 3;
				character[0] = CHARS[random & 63];
				g2d.drawChars(character, 0, 1, x, y+offset);
			}
		}

		Graphics2D g3d = screenshot.createGraphics();
		int x0 = spot_x + veloc_x;
		int y0 = spot_y + veloc_y;
		if (x0 < -(CSIZE<<16) || x0 > (w << 16)) {
			veloc_x = -veloc_x;
			x0 = spot_x;
		}
		if (y0 < -(CSIZE<<16) || y0 > (h << 16)) {
			veloc_y = -veloc_y;
			y0 = spot_y;
		}
		spot_x = x0;
		spot_y = y0;
		veloc_x += ((int) (Math.random() * RND_MULT) - RND_CNTR); 
		veloc_y += ((int) (Math.random() * RND_MULT) - RND_CNTR);
		if (veloc_x < -VLIMIT) {
			veloc_x = -VLIMIT;
		} else if (veloc_x > VLIMIT) {
			veloc_x = VLIMIT;
		}
		if (veloc_y < -VLIMIT) {
			veloc_y = -VLIMIT;
		} else if (veloc_y > VLIMIT) {
			veloc_y = VLIMIT;
		}
		g3d.setPaintMode();
		g3d.setColor(blobColor);
		g3d.setRenderingHint(RenderingHints.KEY_ANTIALIASING, RenderingHints.VALUE_ANTIALIAS_ON);
		g3d.setComposite(composite);
		g3d.fillOval(x0 >> 16, y0 >> 16, CSIZE, CSIZE);
		g2d.drawImage(screenshot, 0, 0, null);
		
		g.drawImage(backbuffer, 0, 0, null);
		g2d.dispose();
		g3d.dispose();
	}

}
