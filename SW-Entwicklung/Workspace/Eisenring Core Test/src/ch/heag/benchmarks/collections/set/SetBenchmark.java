package ch.heag.benchmarks.collections.set;

import java.text.DecimalFormat;
import java.text.SimpleDateFormat;
import java.util.Arrays;
import java.util.Collection;
import java.util.Date;
import java.util.HashSet;
import java.util.Iterator;
import java.util.Set;

import ch.eisenring.core.collections.List;
import ch.eisenring.core.collections.impl.ArrayList;
import ch.eisenring.core.collections.impl.CompactHashSet;
import ch.eisenring.core.collections.impl.SimpleSet;
import ch.eisenring.core.collections.impl.xset.PolySet;
import ch.eisenring.core.datatypes.strings.StringMaker;
import ch.eisenring.core.util.sizeof.MemSize;
import ch.heag.benchmarks.BenchRunResult;
import ch.heag.benchmarks.Benchcase;
import ch.heag.benchmarks.Benchmarkable;
import ch.heag.benchmarks.collections.AbstractCollectionBenchmark;
import ch.heag.benchmarks.collections.CollectionTestData;
import ch.heag.benchmarks.collections.set.cases.*;
//import gnu.trove.set.hash.THashSet;

public class SetBenchmark extends AbstractCollectionBenchmark {
	
	static List<SetProvider> PROVIDERS = new ArrayList<>();

	final static int MAX_BULK_SIZE = 50000;

	static List<CollectionTestData> DATASETS = new ArrayList<>();
	static {
		DATASETS.add(DATA_GOOD);
		//DATASETS.add(DATA_BAD);
		//DATASETS.add(DATA_EVIL);
		//DATASETS.add(DATA_STRING);
	}

	static void testSizeOf() {
		StringMaker b = StringMaker.obtain();
		b.append("sizeOf(\"Test\") = ");
		b.append(MemSize.sizeOf("Test"));
		System.out.println(b.release());
		
		System.out.println("sizeOf(new Integer(0))     = " + MemSize.sizeOf(Integer.valueOf(0)));
		System.out.println("sizeOf(new Object[0])      = " + MemSize.sizeOf(new Object[0]));
		System.out.println("sizeOf(new Object[1000])   = " + MemSize.sizeOf(new Object[1000]));
		System.out.println("sizeOf(new boolean[1000])  = " + MemSize.sizeOf(new boolean[1000]));
		System.out.println("sizeOf(new byte[1000])     = " + MemSize.sizeOf(new byte[1000]));
		System.out.println("sizeOf(new short[1000])    = " + MemSize.sizeOf(new short[1000]));
		System.out.println("sizeOf(new int[1000])      = " + MemSize.sizeOf(new int[1000]));
		System.out.println("sizeOf(new long[1000])     = " + MemSize.sizeOf(new long[1000]));
		
		{
			Object[] test = new Object[1000];
			Arrays.fill(test, test);
			System.out.println("sizeOf(Object[1000]->self) = " + MemSize.sizeOf(test));
		}
	}

	public static void main(String[] argv) {
		//testSizeOf();
		// create set providers
		PROVIDERS.add(new SetProvider() {
			@Override
			public String getTestClassName() {
				return "java.util.HashSet";
			}
			@Override
			public <U> Set<U> createSet(int capacity) {
				return new HashSet<>(capacity);
			}
			@Override
			public <U> Set<U> createSet() {
				return new HashSet<>();
			}
		});
		PROVIDERS.add(new SetProvider() {
			@Override
			public String getTestClassName() {
				return "PolySet";
			}
			@Override
			public <U> Set<U> createSet(int capacity) {
				return new PolySet<>(capacity);
			}
			@Override
			public <U> Set<U> createSet() {
				return new PolySet<>();
			}
		});
//		PROVIDERS.add(new SetProvider() {
//			@Override
//			public String getTestClassName() {
//				return "HashSet2";
//			}
//			@Override
//			public <U> Set<U> createSet(int capacity) {
//				return new HashSet2<>(capacity);
//			}
//			@Override
//			public <U> Set<U> createSet() {
//				return new HashSet2<>();
//			}
//		});
		PROVIDERS.add(new SetProvider() {
			@Override
			public <U> Set<U> createSet() {
				return new CompactHashSet<>();
			}
			@Override
			public <U> Set<U> createSet(int capacity) {
				return new CompactHashSet<>(capacity);
			}
			@Override
			public String getTestClassName() {
				return "ontopia-CompactHashSet";
			}
		});	
//		PROVIDERS.add(new SetProvider() {
//			@Override
//			public <U> Set<U> createSet() {
//				return new FastSet<>();
//			}
//			@Override
//			public <U> Set<U> createSet(int capacity) {
//				return new FastSet<U>();
//			}
//			@Override
//			public String getTestClassName() {
//				return "javalution-FastSet";
//			}
//		});
//		PROVIDERS.add(new SetProvider() {
//			@Override
//			public <U> Set<U> createSet() {
//				return new THashSet<>();
//			}
//			@Override
//			public <U> Set<U> createSet(int capacity) {
//				return new THashSet<>(capacity);
//			}
//			@Override
//			public String getTestClassName() {
//				return "trove-THashSet";
//			}
//		});
		PROVIDERS.add(new SetProvider() {
			@Override
			public String getTestClassName() {
				return "SimpleSet";
			}
			@Override
			public <U> Set<U> createSet(int capacity) {
				return new SimpleSet<U>(capacity);
			}
			
			@Override
			public <U> Set<U> createSet() {
				return new SimpleSet<U>();
			}
		});		
//		PROVIDERS.add(new SetProvider() {
//			@Override
//			public String getTestClassName() {
//				return "LightSet";
//			}
//			@Override
//			public <U> Set<U> createSet(int capacity) {
//				return new LightSet<U>(capacity);
//			}
//			
//			@Override
//			public <U> Set<U> createSet() {
//				return new LightSet<U>();
//			}
//		});		


		// create benchmarks
		final List<Benchcase> suites = new ArrayList<>();
		final int[] sizes = SIZES_FEW;
		suites.addAll(createContainsBenchmarks(DATASETS, PROVIDERS, sizes));
		suites.addAll(createAddBenchmarks(DATASETS, PROVIDERS, sizes));
		suites.addAll(createRemoveBenchmarks(DATASETS, PROVIDERS, sizes));
		suites.addAll(createIterationBenchmarks(DATASETS, PROVIDERS, sizes));
		suites.addAll(createNewBenchmarks(DATASETS, PROVIDERS, sizes));
		suites.addAll(createMiscBenchmarks(DATASETS, PROVIDERS, sizes));
		suites.addAll(createAddAllBenchmarks(DATASETS, PROVIDERS, sizes));
		suites.addAll(createContainsAllBenchmarks(DATASETS, PROVIDERS, sizes));
		suites.addAll(createRemoveAllBenchmarks(DATASETS, PROVIDERS, sizes));
		suites.addAll(createRetainAllBenchmarks(DATASETS, PROVIDERS, sizes));
		
		System.out.print("performing warmup/load");
		warmupSuites(suites);
		System.out.println("done");
		final SimpleDateFormat dateF = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss.SSS");
		System.out.println("Starting test run: " + dateF.format(new Date()));
		
		final DecimalFormat df = new DecimalFormat("0.00");
		String lastDescription = "???";
		
		while (true) {
			boolean printHeader = true;
			for (final Benchcase suite : suites) {
				if (suite.isEmpty())
					continue;
				final Collection<Benchmarkable> benchmarks = suite.getBenchmarks();
				if (printHeader) {
					printHeader = false;
					System.out.print(format("Class:", 50));
					for (final Benchmarkable benchmark : benchmarks) {
						System.out.print(formatr(benchmark.getTestClassName(), 23));
					}
					System.out.println();
				}
				do {
					Iterator<Benchmarkable> itr = benchmarks.iterator();
					if (!itr.hasNext())
						break;
					final Benchmarkable firstInSuite = itr.next();
					final String description = firstInSuite.getDescription(); 
					if (lastDescription.equals(description))
						break;
					System.out.println(repeat('-', 50 + (PROVIDERS.size() * 23)));
					System.out.println(description);
					System.out.println();
					lastDescription = description;
				} while (false);
			
				System.out.print(format(suite.getCaseName(), 50));

				for (final Benchmarkable benchmark : benchmarks) {
					final BenchRunResult result = runBenchmarkable(benchmark);
					final double nanosPerOp = result.getNanosPerOp();
					String part1;
					if (Double.isNaN(nanosPerOp)) {
						part1 = "timeout";
					} else {
						part1 = df.format(nanosPerOp) + "ns";
					}
					String part2;
					if (result.getMemUsed() < 0) {
						part2 = "n/a";
					} else {
						part2 = result.getMemUsed() + "b";
					}
					String msg = formatr(part2, 14) + formatr(part1, 9);
					System.out.print(msg);
				}
				System.out.println();
			}
			break;
		}
		System.out.println("All tests performed: " + dateF.format(new Date()));
	}

	public static String repeat(char c, int count) {
		StringBuilder b = new StringBuilder(count);
		for (int i=0; i<count; ++i)
			b.append(c);
		return b.toString();
	}

	public static String format(Object obj, int length) {
		StringBuilder b = new StringBuilder(length);
		b.append(obj);
		while (b.length() < length)
			b.append(' ');
		return b.toString();
	}

	public static String formatr(Object obj, int length) {
		StringBuilder b = new StringBuilder(length);
		b.append(obj);
		while (b.length() < length)
			b.insert(0, ' ');
		return b.toString();
	}

	final static int[] SIZES_MANY = {
		0, 
		1,       2,       5,
		10,      20,      50,
		100,     200,     500,
		1000,    2000,    5000,
		10000,   20000,   50000,
		100000,  200000,  500000,
		1000000, 2000000, 5000000,
		10000000
	};

	final static int[] SIZES_FEW = {
		0, 
		1,       2,       5,
		10,      200,     1000,
		25000,   150000,  1000000
	};

	final static int[] SIZES_SELECT = {
		10, 100, 200, 1000, 100000
	};

	public static Collection<Benchcase> createIterationBenchmarks(
			final Collection<CollectionTestData> testdatas,
			final Collection<SetProvider> providers,
			final int[] sizes) {
		final List<Benchcase> result = new ArrayList<>();

		// iteration tests
		for (int size : sizes) {
			Benchcase suite = new Benchcase(); 
			result.add(suite);
			for (final SetProvider provider : providers) {
				suite.add(new SetIterate(provider, size));
			}
		}
		
		for (final CollectionTestData data : testdatas) {
			for (int size : sizes) {
				Benchcase suite = new Benchcase(); 
				result.add(suite);
				for (final SetProvider provider : providers) {
					suite.add(new SetAddItrRem(provider, size, data));
				}
			}
		}
		return result;
	}

	public static Collection<Benchcase> createContainsBenchmarks(
			final Collection<CollectionTestData> testdatas,
			final Collection<SetProvider> providers,
			final int[] sizes) {
		final List<Benchcase> result = new ArrayList<>();

		// contains tests
		for (final CollectionTestData data : testdatas) {
			for (int size : sizes) {
				Benchcase suite = new Benchcase(); 
				result.add(suite);
				for (final SetProvider provider : providers) {
					suite.add(new SetContainsFalse(provider, size, data));
				}
			}
		}

		for (final CollectionTestData data : testdatas) {
			for (int size : sizes) {
				Benchcase suite = new Benchcase(); 
				result.add(suite);
				for (final SetProvider provider : providers) {
					suite.add(new SetContainsTrue(provider, size, data));
				}
			}
		}

		for (final CollectionTestData data : testdatas) {
			for (int size : sizes) {
				Benchcase suite = new Benchcase(); 
				result.add(suite);
				for (final SetProvider provider : providers) {
					suite.add(new SetContainsNullFalse(provider, size, data));
				}
			}
		}

		for (final CollectionTestData data : testdatas) {
			for (int size : sizes) {
				Benchcase suite = new Benchcase(); 
				result.add(suite);
				for (final SetProvider provider : providers) {
					suite.add(new SetContainsNullTrue(provider, size, data));
				}
			}
		}
		
		return result;
	}
	
	// hashCode, toArray, toString
	public static Collection<Benchcase> createMiscBenchmarks(
			final Collection<CollectionTestData> testdatas,
			final Collection<SetProvider> providers,
			final int[] sizes) {
		final List<Benchcase> result = new ArrayList<>();

		for (int size : sizes) {
			Benchcase suite = new Benchcase(); 
			result.add(suite);
			for (final SetProvider provider : providers) {
				suite.add(new SetHashCode(provider, size));
			}
		}
		
		for (int size : sizes) {
			Benchcase suite = new Benchcase(); 
			result.add(suite);
			for (final SetProvider provider : providers) {
				suite.add(new SetToString(provider, size));
			}
		}

		for (int size : sizes) {
			Benchcase suite = new Benchcase(); 
			result.add(suite);
			for (final SetProvider provider : providers) {
				suite.add(new SetToArray(provider, size));
			}
		}
		
		return result;
	}

	public static Collection<Benchcase> createNewBenchmarks(
			final Collection<CollectionTestData> testdatas,
			final Collection<SetProvider> providers,
			final int[] sizes) {
		final List<Benchcase> result = new ArrayList<>();

		// new() tests
		{
			Benchcase suite = new Benchcase(); 
			result.add(suite);
			for (final SetProvider provider : providers) {
				suite.add(new SetNewDefault(provider));
			}
		}
		
		for (int size : sizes) {
			Benchcase suite = new Benchcase(); 
			result.add(suite);
			for (final SetProvider provider : providers) {
				suite.add(new SetNewPresized(provider, size));
			}
		}

		for (int size : sizes) {
			Benchcase suite = new Benchcase(); 
			result.add(suite);
			for (final SetProvider provider : providers) {
				suite.add(new SetNewPresizedAdd1(provider, size));
			}
		}

		return result;
	}

	public static Collection<Benchcase> createAddAllBenchmarks(
			final Collection<CollectionTestData> testdatas,
			final Collection<SetProvider> providers,
			final int[] sizes) {
		final List<Benchcase> result = new ArrayList<>();

		// addAll
		for (int size : sizes) {
			Benchcase suite = new Benchcase(); 
			result.add(suite);
			for (final SetProvider provider : providers) {
				if (size > MAX_BULK_SIZE)
					continue;
				suite.add(new SetAddAllFromList(provider, size));
			}
		}

		for (int size : sizes) {
			Benchcase suite = new Benchcase(); 
			result.add(suite);
			for (final SetProvider provider : providers) {
				if (size > MAX_BULK_SIZE)
					continue;
				suite.add(new SetAddAllFromSet(provider, size));
			}
		}
		
		return result;
	}

	public static Collection<Benchcase> createContainsAllBenchmarks(
			final Collection<CollectionTestData> testdatas,
			final Collection<SetProvider> providers,
			final int[] sizes) {
		final List<Benchcase> result = new ArrayList<>();

		// containsAll tests
		for (int size : sizes) {
			Benchcase suite = new Benchcase(); 
			for (final SetProvider provider : providers) {
				if (size > MAX_BULK_SIZE)
					continue;
				suite.add(new SetContainsAllSmallerList(provider, size));
			}
			if (!suite.isEmpty())
				result.add(suite);
		}

		for (int size : sizes) {
			Benchcase suite = new Benchcase(); 
			for (final SetProvider provider : providers) {
				if (size > MAX_BULK_SIZE)
					continue;
				suite.add(new SetContainsAllSmallerSet(provider, size));
			}
			if (!suite.isEmpty())
				result.add(suite);
		}
		
		for (int size : sizes) {
			Benchcase suite = new Benchcase(); 
			for (final SetProvider provider : providers) {
				if (size > MAX_BULK_SIZE)
					continue;
				suite.add(new SetContainsAllLargerList(provider, size));
			}
			if (!suite.isEmpty())
				result.add(suite);
		}

		for (int size : sizes) {
			Benchcase suite = new Benchcase(); 
			for (final SetProvider provider : providers) {
				if (size > MAX_BULK_SIZE)
					continue;
				suite.add(new SetContainsAllListTrue(provider, size));
			}
			if (!suite.isEmpty())
				result.add(suite);
		}

		for (int size : sizes) {
			Benchcase suite = new Benchcase(); 
			for (final SetProvider provider : providers) {
				if (size > MAX_BULK_SIZE)
					continue;
				suite.add(new SetContainsAllLargerSet(provider, size));
			}
			if (!suite.isEmpty())
				result.add(suite);
		}

		for (int size : sizes) {
			Benchcase suite = new Benchcase(); 
			for (final SetProvider provider : providers) {
				if (size > MAX_BULK_SIZE)
					continue;
				suite.add(new SetContainsAllSetTrue(provider, size));
			}
			if (!suite.isEmpty())
				result.add(suite);
		}
		
		return result;
	}
	
	public static Collection<Benchcase> createRetainAllBenchmarks(
			final Collection<CollectionTestData> testdatas,
			final Collection<SetProvider> providers,
			final int[] sizes) {
		final List<Benchcase> result = new ArrayList<>();

		// retainAll tests
		for (int size : sizes) {
			Benchcase suite = new Benchcase(); 
			for (final SetProvider provider : providers) {
				if (size > MAX_BULK_SIZE)
					continue;
				suite.add(new SetRetainHalf(provider, size));
			}
			if (!suite.isEmpty())
				result.add(suite);
		}

		for (int size : sizes) {
			Benchcase suite = new Benchcase(); 
			for (final SetProvider provider : providers) {
				if (size > MAX_BULK_SIZE)
					continue;
				suite.add(new SetRetainNone(provider, size));
			}
			if (!suite.isEmpty())
				result.add(suite);
		}

		for (int size : sizes) {
			Benchcase suite = new Benchcase(); 
			for (final SetProvider provider : providers) {
				if (size > MAX_BULK_SIZE)
					continue;
				suite.add(new SetRetainAllSame(provider, size));
			}
			if (!suite.isEmpty())
				result.add(suite);
		}
		
		return result;
	}

	public static Collection<Benchcase> createRemoveAllBenchmarks(
			final Collection<CollectionTestData> testdatas,
			final Collection<SetProvider> providers,
			final int[] sizes) {
		final List<Benchcase> result = new ArrayList<>();

		// removeAll tests
		for (final CollectionTestData data : testdatas) {
			for (int size : sizes) {
				Benchcase suite = new Benchcase(); 
				for (final SetProvider provider : providers) {
					if (size > MAX_BULK_SIZE)
						continue;
					suite.add(new SetRemoveAllHalf(provider, size, data));
				}
				if (!suite.isEmpty())
					result.add(suite);
			}
		}

		for (final CollectionTestData data : testdatas) {
			for (int size : sizes) {
				Benchcase suite = new Benchcase(); 
				for (final SetProvider provider : providers) {
					if (size > MAX_BULK_SIZE)
						continue;
					suite.add(new SetRemoveAll(provider, size, data));
				}
				if (!suite.isEmpty())
					result.add(suite);
			}
		}

		for (final CollectionTestData data : testdatas) {
			for (int size : sizes) {
				Benchcase suite = new Benchcase(); 
				for (final SetProvider provider : providers) {
					if (size > MAX_BULK_SIZE)
						continue;
					suite.add(new SetRemoveAll2x(provider, size, data));
				}
				if (!suite.isEmpty())
					result.add(suite);
			}
		}
		
		return result;
	}

	public static Collection<Benchcase> createRemoveBenchmarks(
			final Collection<CollectionTestData> testdatas,
			final Collection<SetProvider> providers,
			final int[] sizes) {
		final List<Benchcase> result = new ArrayList<>();

		for (final CollectionTestData data : testdatas) {
			for (int size : sizes) {
				Benchcase suite = new Benchcase(); 
				result.add(suite);
				for (final SetProvider provider : providers) {
					suite.add(new SetRemove(provider, size, data));
				}
			}
		}

		for (final CollectionTestData data : testdatas) {
			for (int size : sizes) {
				Benchcase suite = new Benchcase(); 
				result.add(suite);
				for (final SetProvider provider : providers) {
					suite.add(new SetRemoveMiss(provider, size, data));
				}
			}
		}
		
		return result;
	}

	public static Collection<Benchcase> createAddBenchmarks(
			final Collection<CollectionTestData> testdatas,
			final Collection<SetProvider> providers,
			final int[] sizes) {
		final List<Benchcase> result = new ArrayList<>();

		for (final CollectionTestData data : testdatas) {
			for (int size : sizes) {
				Benchcase suite = new Benchcase(); 
				result.add(suite);
				for (final SetProvider provider : providers) {
					suite.add(new SetAddDistinctPresized(provider, size, data));
				}
			}
		}

		for (final CollectionTestData data : testdatas) {
			for (int size : sizes) {
				Benchcase suite = new Benchcase(); 
				result.add(suite);
				for (final SetProvider provider : providers) {
					suite.add(new SetAddDistinct(provider, size, data));
				}
			}
		}

		for (final CollectionTestData data : testdatas) {
			for (int size : sizes) {
				Benchcase suite = new Benchcase(); 
				result.add(suite);
				for (final SetProvider provider : providers) {
					suite.add(new SetAddAllDupes(provider, size, data));
				}
			}
		}

		for (final CollectionTestData data : testdatas) {
			for (int size : sizes) {
				Benchcase suite = new Benchcase(); 
				result.add(suite);
				for (final SetProvider provider : providers) {
					suite.add(new SetAddHalfDupes(provider, size, data));
				}
			}
		}

		return result;
	}

}
