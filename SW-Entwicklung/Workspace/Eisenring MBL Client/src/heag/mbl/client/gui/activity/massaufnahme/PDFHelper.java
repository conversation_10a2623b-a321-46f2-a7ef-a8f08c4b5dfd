package heag.mbl.client.gui.activity.massaufnahme;

import heag.mbl.client.gui.pdf.PDFOverlayBase;

import java.awt.Color;
import java.awt.geom.Point2D;

import org.faceless.pdf2.PDFFont;
import org.faceless.pdf2.PDFPage;
import org.faceless.pdf2.PDFStyle;
import org.faceless.pdf2.StandardFont;

class PDFHelper {
	
	/**
	 * Stamps text into page at given location. The location can be specified
	 * relative from any page border, positive means from left/bottom,
	 * negative means from right/top. 
	 */
	public static void stampPage(final PDFPage page, final String text, final Point2D location, final Color fgColor, final Color bgColor, final float fontSize) {
		// determine location where to place the text
		float w = page.getWidth();
		float h = page.getHeight();
		float tw = PDFOverlayBase.getTextWidth(text, fontSize);
		float th = fontSize;
		float x = (float) location.getX();
		float y = (float) location.getY();
		if (x < 0) {
			x = w + x - tw;
		}
		if (y < 0) {
			y = h + y - th;
		}
		final PDFFont font = new StandardFont(StandardFont.HELVETICABOLD);
		final PDFStyle s = new PDFStyle();
		s.setTextAlign(PDFStyle.TEXTALIGN_LEFT);
		s.setFont(font, fontSize);
		page.seekEnd();
		page.setStyle(s);
		if (bgColor != null) {
			s.setFillColor(bgColor);
			s.setLineColor(bgColor);
			page.drawRectangle(x, y, x + tw, y + th);
		}
		if (fgColor != null) {
			s.setFillColor(fgColor);
			s.setLineColor(fgColor);
			page.drawText(text, x, y);
		}
	}

	public static void stampPages(final java.util.Collection<PDFPage> pages, final String text, final Point2D location, final Color fgColor, final Color bgColor, final float fontSize) {
		for (final PDFPage page : pages) {
			stampPage(page, text, location, fgColor, bgColor, fontSize);
		}
	}

}
