<project>

    <include file="${ant.home}/tasks/customtasks.xml"/>

    <property name="base.dir" value="../../../Eisenring Dispo"/>
    <property name="workspace.dir" value="../../../SW-Entwicklung/Workspace"/>
    <property name="lib.dir" value="${base.dir}/Client/lib"/>
    <property name="build.dir" value="ant_build"/>
    <property name="classes.dir" value="${build.dir}/classes"/>
    <property name="jar.file" value="client.jar"/>
    <property name="update.dir" value="${base.dir}/Server/update/client"/>

    <path id="classpath">
        <fileset dir="${lib.dir}" includes="**/*.jar" excludes="${jar.file},printing.jar"/>
    </path>
    
    <target name="clean">
        <delete dir="${build.dir}"/>
        <delete file="${lib.dir}/${jar.file}"/>
    </target>

    <target name="compile" depends="clean">
        <mkdir dir="${classes.dir}"/>
        <javac srcdir="
		${workspace.dir}/Eisenring Core/src;
		${workspace.dir}/Eisenring Commons/src;
		${workspace.dir}/Eisenring Network/src;
		${workspace.dir}/Eisenring GUI/src;
		${workspace.dir}/Eisenring JDBC;
		${workspace.dir}/Eisenring Model Shared;
		${workspace.dir}/Eisenring Model GUI;
		${workspace.dir}/Eisenring App Shared;
		${workspace.dir}/Eisenring App Client;
		${workspace.dir}/Eisenring TAPI Client/src;
		${workspace.dir}/Eisenring PRT Shared;
		${workspace.dir}/Eisenring PRT Client;
		${workspace.dir}/Eisenring EMail Shared/src;
		${workspace.dir}/Eisenring EMail Client/src;
		${workspace.dir}/Eisenring User Shared/src;
		${workspace.dir}/Eisenring User Client/src;
		${workspace.dir}/Eisenring HUO Shared/src;
		${workspace.dir}/Eisenring HUO Client/src;
		${workspace.dir}/Eisenring Statistik Shared/src;
		${workspace.dir}/Eisenring Statistik Client/src;
		${workspace.dir}/Eisenring Logiware Shared;
		${workspace.dir}/Eisenring Logiware GUI;
		${workspace.dir}/Eisenring DMS Service/src;
		${workspace.dir}/Eisenring DMS Shared/src;
		${workspace.dir}/Eisenring DMS Shared Client/src;
		${workspace.dir}/Eisenring DMS ClientService/src;
		${workspace.dir}/Eisenring DSP Public/src;
		${workspace.dir}/Eisenring Dispo Service/src;
		${workspace.dir}/Eisenring Dispo Shared/src;
    		src"
        	destdir="${classes.dir}"
        	classpathref="classpath"
	        includeAntRuntime="no"
        	includeJavaRuntime="yes"
        	debug="true"
			encoding="UTF-8"
        />
      <touch datetime="10/10/2010 10:10 am">
        <fileset dir="${classes.dir}"/>
      </touch>
    </target>

    <target name="jar" depends="compile">
        <zip destfile="${lib.dir}/${jar.file}" level="9">
            <fileset includes="**/*" dir="${classes.dir}"/>
            <fileset excludes="**/*.java" dir="${workspace.dir}/Eisenring Core/src"/>
            <fileset excludes="**/*.java" dir="${workspace.dir}/Eisenring Commons/src"/>
            <fileset excludes="**/*.java" dir="${workspace.dir}/Eisenring Network/src"/>
            <fileset excludes="**/*.java" dir="${workspace.dir}/Eisenring GUI/src"/>
            <fileset excludes="**/*.java" dir="${workspace.dir}/Eisenring JDBC/src"/>
            <fileset excludes="**/*.java" dir="${workspace.dir}/Eisenring Model Shared/src"/>
            <fileset excludes="**/*.java" dir="${workspace.dir}/Eisenring Model GUI/src"/>
            <fileset excludes="**/*.java" dir="${workspace.dir}/Eisenring App Shared/src"/>
            <fileset excludes="**/*.java" dir="${workspace.dir}/Eisenring App Client/src"/>
            <fileset excludes="**/*.java" dir="${workspace.dir}/Eisenring TAPI Client/src"/>
            <fileset excludes="**/*.java" dir="${workspace.dir}/Eisenring PRT Shared/src"/>
            <fileset excludes="**/*.java" dir="${workspace.dir}/Eisenring PRT Client/src"/>
            <fileset excludes="**/*.java" dir="${workspace.dir}/Eisenring EMail Shared/src"/>
            <fileset excludes="**/*.java" dir="${workspace.dir}/Eisenring EMail Client/src"/>
            <fileset excludes="**/*.java" dir="${workspace.dir}/Eisenring User Shared/src"/>
            <fileset excludes="**/*.java" dir="${workspace.dir}/Eisenring User Client/src"/>
            <fileset excludes="**/*.java" dir="${workspace.dir}/Eisenring HUO Shared/src"/>
            <fileset excludes="**/*.java" dir="${workspace.dir}/Eisenring HUO Client/src"/>
            <fileset excludes="**/*.java" dir="${workspace.dir}/Eisenring Statistik Shared/src"/>
            <fileset excludes="**/*.java" dir="${workspace.dir}/Eisenring Statistik Client/src"/>
            <fileset excludes="**/*.java" dir="${workspace.dir}/Eisenring Logiware Shared/src"/>
            <fileset excludes="**/*.java" dir="${workspace.dir}/Eisenring Logiware GUI/src"/>
            <fileset excludes="**/*.java" dir="${workspace.dir}/Eisenring DMS Service/src"/>
            <fileset excludes="**/*.java" dir="${workspace.dir}/Eisenring DMS Shared/src"/>
            <fileset excludes="**/*.java" dir="${workspace.dir}/Eisenring DMS Shared Client/src"/>
            <fileset excludes="**/*.java" dir="${workspace.dir}/Eisenring DMS ClientService/src"/>
            <fileset excludes="**/*.java" dir="${workspace.dir}/Eisenring DSP Public/src"/>
            <fileset excludes="**/*.java" dir="${workspace.dir}/Eisenring Dispo Service/src"/>
            <fileset excludes="**/*.java" dir="${workspace.dir}/Eisenring Dispo Shared/src"/>
            <fileset excludes="**/*.java" dir="src"/>
        </zip>
    </target>

    <target name="launch4j" depends="jar">
	<launch4j configFile="${base.dir}/Source/Launch4j/Client_New.xml" outfile="${base.dir}/Client/bin/Eisenring-Dispo.exe"/>
    </target>

    <target name="build" depends="jar">
        <delete dir="${build.dir}"/>
    </target>

    <target name="release" depends="build">    
	<delete dir="${update.dir}"/>
	<mkdir dir="${update.dir}/bin"/>
	<mkdir dir="${update.dir}/lib"/>
	<mkdir dir="${update.dir}/help"/>
	<mkdir dir="${update.dir}/data"/>
	<copy todir="${update.dir}/bin">
	    <fileset dir="${base.dir}/Client/bin"/>
	</copy>
	<copy todir="${update.dir}/lib">
	    <fileset dir="${base.dir}/Client/lib"/>
	</copy>
	<copy todir="${update.dir}/help">
	    <fileset dir="${base.dir}/Client/help"/>
        </copy>
	<copy todir="${update.dir}/data">
	    <fileset dir="${base.dir}/Client/data"/>
        </copy>
    </target>
       
</project>
