package ch.eisenring.dispo.client.gui.about;

import java.awt.Dimension;
import java.awt.Graphics;

import javax.swing.Icon;

import ch.eisenring.gui.components.HEAGPanel;

@SuppressWarnings("serial")
class DSPImagePanel extends HEAGPanel {

	private Icon icon;

	public DSPImagePanel() {
	}

	public DSPImagePanel(final Icon icon) {
		setImage(icon);
	}

	public void setImage(final Icon icon) {
		if (this.icon != icon) {
			this.icon = icon;
			if (icon != null) {
				final int w = icon.getIconWidth();
				final int h = icon.getIconHeight();
				setPreferredSize(new Dimension(w, h));
			}
			repaint();
		}
	}

	@Override
	protected void paintComponent(final Graphics g) {
		super.paintComponent(g);
		final Icon icon = this.icon;
		if (icon != null) {
			int x = (getWidth() - icon.getIconWidth()) >> 1;
			int y = (getHeight() - icon.getIconHeight()) >> 1;
			icon.paintIcon(this, g, x, y);
		}
	}

}
