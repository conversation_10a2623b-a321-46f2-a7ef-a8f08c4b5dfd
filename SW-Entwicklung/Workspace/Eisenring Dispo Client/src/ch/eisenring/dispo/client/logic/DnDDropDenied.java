package ch.eisenring.dispo.client.logic;

import ch.eisenring.dispo.client.Client;
import ch.eisenring.dispo.client.gui.dnd.DnDEvent;
import ch.eisenring.dispo.client.gui.dnd.DnDHintDrop;
import ch.eisenring.dispo.client.gui.grid.GridElement;

public final class DnDDropDenied extends DnDHintDrop {

	public final static DnDDropDenied INSTANCE = new DnDDropDenied();
	
	private DnDDropDenied() {
	}

	@Override
	public GridElement getDropDelegate(final Client client, final DnDEvent event) {
		return null;
	}

	@Override
	public boolean isCopyDrop(final DnDEvent event) {
		return false;
	}

}
