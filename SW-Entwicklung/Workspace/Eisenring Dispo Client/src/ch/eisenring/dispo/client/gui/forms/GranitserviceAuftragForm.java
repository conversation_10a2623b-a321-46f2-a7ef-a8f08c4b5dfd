package ch.eisenring.dispo.client.gui.forms;

import java.awt.GridBagLayout;
import java.awt.event.FocusEvent;
import java.awt.event.FocusListener;
import java.util.Date;

import ch.eisenring.core.barcode.Code128;
import ch.eisenring.core.codetype.AbstractCode;
import ch.eisenring.core.collections.Map;
import ch.eisenring.core.datatypes.primitives.ConversionUtil;
import ch.eisenring.core.datatypes.strings.StringMaker;
import ch.eisenring.core.datatypes.strings.Strings;
import ch.eisenring.core.valueset.ValueSet;
import ch.eisenring.dispo.client.Client;
import ch.eisenring.dispo.shared.model.AbstractModel;
import ch.eisenring.dispo.shared.model.Auftrag;
import ch.eisenring.dispo.shared.model.FormModel;
import ch.eisenring.dispo.shared.resource.reports.Reports;
import ch.eisenring.gui.components.CompoundLinePanel;
import ch.eisenring.gui.components.HEAGCheckBox;
import ch.eisenring.gui.components.HEAGCodeComboBox;
import ch.eisenring.gui.components.HEAGComboBox;
import ch.eisenring.gui.components.HEAGDateField;
import ch.eisenring.gui.components.HEAGLabel;
import ch.eisenring.gui.components.HEAGPanel;
import ch.eisenring.gui.components.HEAGTextArea;
import ch.eisenring.gui.components.HEAGTextField;
import ch.eisenring.gui.components.Separator;
import ch.eisenring.gui.util.LayoutUtil;
import ch.eisenring.logiware.code.soft.LWMonteurPfyCode;
import ch.eisenring.print.shared.resource.ReportResource;
import ch.eisenring.tapi.client.gui.PhoneNumberField;

@SuppressWarnings("serial")
public class GranitserviceAuftragForm extends FormPanelBase {

	public static final String VK_BEMERKUNGEN = "Bemerkungen";
	public static final String VK_MONTEUR = "Monteur";
	public static final String VK_SONSTIGES = "Sonstiges";
	public static final String VK_RICHTEN = "Richten";
	public static final String VK_LIEFERUNG = "Lieferung";
	public static final String VK_NACHPOLITUR = "Nachpolitur";
	public static final String VK_FLECK = "Fleck";
	public static final String VK_RISS = "Riss";
	public static final String VK_STEINKITTFUGE = "Steinkittfuge";
	public static final String VK_SILIKONFUGE = "Silikonfuge";
	public static final String VK_DRINGLICHKEIT = "Dringlichkeit";
	public static final String VK_BEWOHNT = "Bewohnt";
	public static final String VK_RUECKMELDUNG = "Rückmeldung";
	public static final String VK_SACHBEARBEITER = "Sachbearbeiter";
	public static final String VK_GARANTIE = "Garantie";
	public static final String VK_NATEL = "Natel";
	public static final String VK_TELEFON = "Telefon";
	public final static String VK_OBJEKT = "Objekt";
	public final static String VK_PROJEKTNUMMER = "Projektnummer";
	public final static String VK_EINGANG = "Eingangsdatum";
	public final static String VK_SONSTIGESTEXT = "SonstigesText";
	public final static String VK_OBJEKTBETREUER = "ObjektBetreuer";
	public final static String VK_KONTAKTPERSON = "KontaktPerson";
	public final static String VK_AUFTRAGNUMMER = "Auftragnummer";
	
	public final static String[] ITEMS_DRINGLICHKEIT = { "A", "B", "C" };

	protected final HEAGComboBox<Object> cmbDringlichkeit = new HEAGComboBox<Object>(new StringBinding(VK_DRINGLICHKEIT, 60), (Object[]) ITEMS_DRINGLICHKEIT);
	protected final HEAGDateField datEingang = new HEAGDateField(new DateBinding(VK_EINGANG));
	protected final HEAGCodeComboBox<LWMonteurPfyCode> cmbMonteur = new HEAGCodeComboBox<LWMonteurPfyCode>(LWMonteurPfyCode.class, new FormBinding() {
		@Override
		public void setValue(final ValueSet model, final Object value) {
			Object k = AbstractCode.getKey((AbstractCode) value, null);
			String s = ConversionUtil.convert(k, Strings.NULL);
			model.add(VK_MONTEUR, s); 
		}
		@Override
		public Object getValue(final ValueSet model) {
			String k = model.getString(VK_MONTEUR, null); 
			return AbstractCode.getByKey(k, cmbMonteur.getValueClass()); 
		}
	});
	protected final HEAGTextField txtAuftragNr = new HEAGTextField(new StringBinding(VK_AUFTRAGNUMMER, 30));
	protected final HEAGTextField txtSonstiges = new HEAGTextField(new StringBinding(VK_SONSTIGESTEXT, 60));
	protected final HEAGTextArea txtBemerkungen = new HEAGTextArea(new StringBinding(VK_BEMERKUNGEN, 750));
	protected final HEAGTextField txtProjektNr = new HEAGTextField(new StringBinding(VK_PROJEKTNUMMER, 30));
	protected final HEAGTextArea txtObjekt = new HEAGTextArea(new StringBinding(VK_OBJEKT, 4000));
	protected final HEAGTextArea txtKontaktPerson = new HEAGTextArea(new StringBinding(VK_KONTAKTPERSON, 4000));
	protected final PhoneNumberField telTelefon = new PhoneNumberField(new StringBinding(VK_TELEFON, 120));
	protected final PhoneNumberField telNatel = new PhoneNumberField(new StringBinding(VK_NATEL, 120));
	protected final PhoneNumberField telObjektbetreuer = new PhoneNumberField(new StringBinding(VK_OBJEKTBETREUER, 60));
	protected final PhoneNumberField telSachbearbeiter = new PhoneNumberField(new StringBinding(VK_SACHBEARBEITER, 60));
	protected final HEAGCheckBox ckbGarantie = new HEAGCheckBox("Garantiefall", new BoolBinding(VK_GARANTIE));
	protected final HEAGCheckBox ckbRueckmeldung = new HEAGCheckBox("Rückmeldung", new BoolBinding(VK_RUECKMELDUNG));
	protected final HEAGCheckBox ckbBewohnt = new HEAGCheckBox("Bewohnt", new BoolBinding(VK_BEWOHNT));
	protected final HEAGCheckBox ckbSilikonfuge = new HEAGCheckBox("Silikonfuge", new BoolBinding(VK_SILIKONFUGE));
	protected final HEAGCheckBox ckbSteinkittfuge = new HEAGCheckBox("Steinkittfuge", new BoolBinding(VK_STEINKITTFUGE));
	protected final HEAGCheckBox ckbRiss = new HEAGCheckBox("Riss", new BoolBinding(VK_RISS));
	protected final HEAGCheckBox ckbFleck = new HEAGCheckBox("Fleck", new BoolBinding(VK_FLECK));
	protected final HEAGCheckBox ckbNachpolitur = new HEAGCheckBox("Nachpolitur", new BoolBinding(VK_NACHPOLITUR));
	protected final HEAGCheckBox ckbLieferung = new HEAGCheckBox("Lieferung", new BoolBinding(VK_LIEFERUNG));
	protected final HEAGCheckBox ckbRichten = new HEAGCheckBox("Richten", new BoolBinding(VK_RICHTEN));
	protected final HEAGCheckBox ckbSonstiges = new HEAGCheckBox("", new BoolBinding(VK_SONSTIGES));
	
	protected final FocusListener sonstigesFocusListener = new FocusListener() {
		@Override
		public void focusGained(final FocusEvent event) {
			focusLost(event);
		}
		@Override
		public void focusLost(final FocusEvent event) {
			ckbSonstiges.setSelected(!Strings.isEmpty(txtSonstiges.getText()));
		}
	};
	
	public GranitserviceAuftragForm(final Client client) {
		super(client);
	}

	@Override
	protected void initComponents() {
		ckbSonstiges.setEnabled(false);
		txtSonstiges.addFocusListener(sonstigesFocusListener);
	}
	
	@Override
	protected void initLayout() {
		removeAll();
		setLayout(new GridBagLayout());
		final LayoutUtil l = new LayoutUtil(4);
		
		add(new HEAGLabel("Erstellt/Geändert"), l.label());
		add(txtTrackingInfo, l.field());
		add(Separator.create(), l.separator());
		
		add(new HEAGLabel("Eingangsdatum"), l.label());
		add(datEingang, l.fixed(1));
		l.nextLine();
		
		add(new HEAGLabel("Kom.-Nr / Auftrag-Nr."), l.label());
		add(txtProjektNr, l.field(1));
		add(txtAuftragNr, l.field(1));
		l.nextLine();
		
		add(new HEAGLabel("Objekt"), l.label());
		add(txtObjekt, l.field());
		l.label();
		add(ckbBewohnt, l.field());
		
		add(new HEAGLabel("Kontaktperson"), l.label());
		add(txtKontaktPerson, l.field());

		add(new HEAGLabel("Telefon Kunde"), l.label());
		add(telTelefon, l.field());
		
		add(new HEAGLabel("Telefon Bauführer"), l.label());
		add(telNatel, l.field());
		
		add(new HEAGLabel("Sachbearbeiter"), l.label());
		add(telSachbearbeiter, l.field());
		
		add(new HEAGLabel("Objektbetreuer"), l.label());
		add(telObjektbetreuer, l.field());

		add(new HEAGLabel("Dringlichkeit"), l.label());
		add(cmbDringlichkeit, l.field(1));
		l.nextLine();
		
		l.label();
		add(ckbRueckmeldung, l.field(1));
		add(ckbGarantie, l.field());
		
		add(Separator.create(), l.separator());
		l.label();
		add(ckbSilikonfuge, l.field(1));
		add(ckbSteinkittfuge, l.field(1));
		add(ckbRiss, l.field());
		
		add(new HEAGLabel("Servicegrund"), l.label());
		add(ckbFleck, l.field(1));
		add(ckbNachpolitur, l.field(1));
		add(ckbLieferung, l.field());
		
		final CompoundLinePanel pnlSonstiges = new CompoundLinePanel();
		pnlSonstiges.addFixed(ckbSonstiges);
		pnlSonstiges.addField(txtSonstiges, 1);
		l.label();
		add(ckbRichten, l.field(1));
		add(pnlSonstiges, l.field());
		
		add(new HEAGLabel("Bemerkungen"), l.label());
		add(txtBemerkungen, l.field());
		add(Separator.create(), l.separator());

		add(new HEAGLabel("Service-Monteur"), l.label());
		add(cmbMonteur, l.field(1));
		l.nextLine();
		
		add(new HEAGPanel(), l.area());
	}
	
	@Override
	public void setInitialValues(final AbstractModel model, final ValueSet valueSet) {
		super.setInitialValues(model, valueSet);
		final Auftrag auftrag = (Auftrag) model;
		valueSet.add(VK_EINGANG, new Date());
		valueSet.add(VK_PROJEKTNUMMER, auftrag.getProjektnummer());
		valueSet.add(VK_OBJEKT, auftrag.getProjektbezeichnung());
		{ // telefon bauherr + name
			final String s = combinedContact(
					auftrag.getNameBauherr(),
					auftrag.getReserve6(),
					auftrag.getPhoneBauherr(),
					auftrag.getMobileBauherr());
			if (!Strings.isEmpty(s)) {
				valueSet.add(VK_TELEFON, s);
			}
		}
		{ // telefon bauführer + name
			final String s = combinedContact(
					auftrag.getNameBaufuehrer(),
					auftrag.getEMailBaufuehrer(),
					auftrag.getPhoneBaufuehrer(),
					auftrag.getMobileBaufuehrer());
			if (!Strings.isEmpty(s)) {
				valueSet.add(VK_NATEL, s);
			}
		}
		valueSet.add(VK_SACHBEARBEITER, AbstractCode.getText(auftrag.getKundenberater(), null));
		valueSet.add(VK_OBJEKTBETREUER, AbstractCode.getText(auftrag.getObjektbetreuer(), null));
		valueSet.add(VK_BEWOHNT, auftrag.getBezugsDatum() != null);
		final LWMonteurPfyCode monteur = auftrag.getMonteurPfyn();
		if (monteur != null) {
			valueSet.add(VK_MONTEUR, monteur.getKey());
		}
		valueSet.add(VK_KONTAKTPERSON, auftrag.getKontaktperson());
		valueSet.add(VK_AUFTRAGNUMMER, Strings.toString(auftrag.getAuftragnummer()));
	}

	static String combinedContact(
			final CharSequence name,
			final CharSequence email,
			final CharSequence phone,
			final CharSequence mobile) {
		final StringMaker b = StringMaker.obtain();
		if (!Strings.isEmpty(phone)) {
			if (b.length() > 0)
				b.append(" / ");
			b.append(phone);
		}
		if (!Strings.isEmpty(name)) {
			if (b.length() > 0)
				b.append(" / ");
			b.append(name);
		}
		if (!Strings.isEmpty(mobile)) {
			if (b.length() > 0)
				b.append(" / ");
			b.append(mobile);
		}
		if (!Strings.isEmpty(email)) {
			if (b.length() > 0)
				b.append(" / ");
			b.append(email);
		}
		return b.release();
	}

	@Override
	public ReportResource getReport() {
		return Reports.REPORT_GRANITSERVICEAUFTRAG;
	}

	@Override
	public void populateParameters(final Map<String, Object> map,
			final ValueSet valueSet, final FormModel model) {
		super.populateParameters(map, valueSet, model);
		map.put("Eingangsdatum", valueSet.getDate(VK_EINGANG, null));
		map.put("Dringlichkeit", valueSet.getString(VK_DRINGLICHKEIT, ITEMS_DRINGLICHKEIT[0]));
		map.put("Projektnummer", valueSet.getString(VK_PROJEKTNUMMER, ""));
		map.put("Objekt", valueSet.getString(VK_OBJEKT, ""));
		map.put("Telefon", valueSet.getString(VK_TELEFON, ""));
		map.put("Natel", valueSet.getString(VK_NATEL, ""));
		map.put("Garantiefall", valueSet.getBoolean(VK_GARANTIE, false));
		map.put("Sachbearbeiter", valueSet.getString(VK_SACHBEARBEITER, ""));
		map.put("Rueckmeldung", valueSet.getBoolean(VK_RUECKMELDUNG, false));
		map.put("Silikonfuge", valueSet.getBoolean(VK_SILIKONFUGE, false));
		map.put("Steinkittfuge", valueSet.getBoolean(VK_STEINKITTFUGE, false));
		map.put("Riss", valueSet.getBoolean(VK_RISS, false));
		map.put("Fleck", valueSet.getBoolean(VK_FLECK, false));
		map.put("Nachpolitur", valueSet.getBoolean(VK_NACHPOLITUR, false));
		map.put("Lieferung", valueSet.getBoolean(VK_LIEFERUNG, false));
		map.put("Richten", valueSet.getBoolean(VK_RICHTEN, false));
		map.put("Sonstiges", valueSet.getBoolean(VK_SONSTIGES, false));
		map.put("Bemerkungen", valueSet.getString(VK_BEMERKUNGEN, ""));
		final String key = valueSet.getString(VK_MONTEUR, null);
		final LWMonteurPfyCode type = (LWMonteurPfyCode) AbstractCode.getByKey(key, LWMonteurPfyCode.class);
		map.put("Monteur", type == null ? "" : type.getText());
		map.put("Sonstigestext", valueSet.getString(VK_SONSTIGESTEXT, ""));
		map.put("Objektbetreuer", valueSet.getString(VK_OBJEKTBETREUER, ""));
		map.put("Bewohnt", valueSet.getBoolean(VK_BEWOHNT, false));
		map.put("KontaktPerson", valueSet.getString(VK_KONTAKTPERSON, ""));
		map.put("Auftragnummer", valueSet.getString(VK_AUFTRAGNUMMER, ""));

		// create document barcode
		final String barcode; {
			final StringMaker b = StringMaker.obtain();
			b.append("H02");
			b.append(valueSet.get(VK_PROJEKTNUMMER, ""));
			barcode = b.release();
		}
		map.put("Barcode", Code128.getInstance().encode(barcode));
		map.put("Clearcode", barcode);
	}

}
