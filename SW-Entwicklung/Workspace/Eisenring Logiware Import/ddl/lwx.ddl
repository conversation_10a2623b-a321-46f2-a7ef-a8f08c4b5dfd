
-- ------------------------------------------------------------------
--
-- Logiware PAuftrag HEAG Extension for Hausuebersicht
--
-- ------------------------------------------------------------------
CREATE TABLE LWX_PAUFTRAG (
  PAuftrNr                     VARCHAR(16) NOT NULL,
  Id_GSE_PA                    CHAR(4),
  Haus_BemerkungUebersicht     VARCHAR(1000),
  OUS_Bemerkung                VARCHAR(1000),
  Haus_<PERSON><PERSON>chtBezeichnung      VARCHAR(60),
  Haus_KuechenMontageInfo      VARCHAR(60),
  Haus_GranitMontageInfo       VARCHAR(60),
  Haus_ObjektSituation         VARBINARY(MAX),
  Haus_<PERSON>umnHint              INT,
  Haus_RowHint                 INT,
  EtappeRowId                  BIGINT
);

CREATE UNIQUE INDEX IDX_LWX_PAUFTRAG ON LWX_PAUFTRAG(PAuftrNr, Id_GSE_PA);

GRANT ALL ON LWX_PAUFTRAG TO PUBLIC;

-- ------------------------------------------------------------------
--
-- Logiware Etappe HEAG Extension
--
-- ------------------------------------------------------------------
CREATE TABLE LWX_ETAPPE (
  rowId           BIGINT NOT NULL,
  PAuftrNr        VARCHAR(16) NOT NULL,
  Id_GSE_PA       CHAR(4),
  EtappeStart     DATE NOT NULL,
  EtappeEnde      DATE NOT NULL,
  Bezeichnung     VARCHAR(60) NOT NULL,
  OrdnungsNr      INT NOT NULL,
  UebergabeDatum  DATE
);

ALTER TABLE LWX_ETAPPE ADD PRIMARY KEY (rowId);

GRANT ALL ON LWX_ETAPPE TO PUBLIC;

-- ------------------------------------------------------------------
--
-- ProjektKonditionen Extension
--
-- ------------------------------------------------------------------
-- DROP TABLE [dbo].[LWX_PROJEKTKONDITIONEN];

CREATE TABLE [dbo].[LWX_PROJEKTKONDITIONEN] (
  [PAuftrNr]    [varchar](16) NOT NULL,
  [PKondId]     [int] NOT NULL,
  [Value]       [varchar](60) NOT NULL,
  CONSTRAINT    [PrimaryKey] PRIMARY KEY (
    [PAuftrNr] ASC,
    [PKondId] ASC
  )
);

GRANT ALL ON LWX_PROJEKTKONDITIONEN TO PUBLIC;

ALTER TABLE [HEAG_DSP_Produktiv].[dbo].[LWX_PAUFTRAG]
	ADD [EPK_AdrVMBasis]			varchar(256)
	  , [EPK_AdrVMBasisSchuldner]	varchar(256)
	  , [EPK_AdrVMBasisReEmpf]		varchar(256)
	  , [EPK_AdrVMMP]				varchar(256)
	  , [EPK_AdrVMMPSchuldner]		varchar(256)
	  , [EPK_AdrVMMPReEmpf]			varchar(256)
	  , [EPK_AdrOfferteAn]			varchar(256)
	  , [EPK_AdrABAn]				varchar(256)
	  , [EPK_AdrIPAn]				varchar(256)
	  , [EPK_Bemerkung]				varchar(1000);

ALTER TABLE [HEAG_DSP_Produktiv].[dbo].[LWX_PAUFTRAG]
    ADD [EPK_AdrFreigabeText]       varchar(256)
      , [EPK_AdrKundenRabattText]   varchar(256)
      , [EPK_AdrKaeuferRabattText]  varchar(256);
