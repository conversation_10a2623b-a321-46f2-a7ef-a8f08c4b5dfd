package ch.eisenring.dispo.server.service.statistics;

import static ch.eisenring.dispo.shared.codetables.EmploymentRole.MITARBEITER_MONTEUR;
import static ch.eisenring.dispo.shared.codetables.EmploymentType.EMPTYPE_INTERNAL;

import java.util.Collections;
import java.util.Comparator;
import java.util.Date;
import java.util.Iterator;

import ch.eisenring.core.codetype.AbstractCode;
import ch.eisenring.core.collections.Collection;
import ch.eisenring.core.collections.Map;
import ch.eisenring.core.collections.impl.ArrayList;
import ch.eisenring.core.collections.impl.HashMap;
import ch.eisenring.core.csv.CSVFile;
import ch.eisenring.core.csv.CSVLine;
import ch.eisenring.core.datatypes.date.TimestampUtil;
import ch.eisenring.core.datatypes.strings.Strings;
import ch.eisenring.dispo.server.DSPServer;
import ch.eisenring.dispo.shared.codetables.EmploymentType;
import ch.eisenring.dispo.shared.model.MasterMitarbeiter;
import ch.eisenring.dispo.shared.model.ModelContext;
import ch.eisenring.logiware.code.soft.GSECode;
import ch.eisenring.logiware.code.soft.LWMonteurSirCode;
import ch.eisenring.presento.server.api.PresentoCache;
import ch.eisenring.presento.server.api.PresentoPerson;
import ch.eisenring.presento.server.api.PresentoRoleCode;
import ch.eisenring.statistics.shared.data.DataPoint;
import ch.eisenring.statistics.shared.network.PacketStatisticRequest;
import ch.eisenring.statistics.shared.network.PacketStatisticResponse;

public class BestandMonteureAuswertung extends StatisticsBase {

	protected static class Monteur {
		public String shortName;
		public String firstNamePresento = "";
		public String lastNamePresento = "";
		public String firstNameDispo = "";
		public String lastNameDispo = "";
		public String fullNameLogi = "";
		public String codeLogiware = "";
		public String codeDispo = "";
		public String codePresento = "";
		public EmploymentType type;
		public double value;
		public GSECode gse;
	}

	protected final static Comparator<Monteur> MONTEUR_COMPARATOR = new Comparator<Monteur>() {
		@Override
		public int compare(final Monteur m1, final Monteur m2) {
			return m1.shortName.compareTo(m2.shortName);
		}
	};

	private final Map<String, Monteur> monteurMap = new HashMap<>();
	
	public BestandMonteureAuswertung(final DSPServer server) {
		super(server);
	}

	@Override
	public PacketStatisticResponse createResponse(final PacketStatisticRequest packet) {
		final Date dateFrom = packet.getDateFrom() == null ? new Date() : packet.getDateFrom();
		
		for (final PresentoPerson person : getPresentoMonteure(server)) {
			if (PresentoRoleCode.MONTEUR.equals(person.getRole())) {
				final Monteur monteur = getOrCreate(person.getShortName());
				if (monteur == null)
					continue;
				monteur.firstNamePresento = person.getFirstName();
				monteur.lastNamePresento = person.getLastName();
				monteur.codePresento = Strings.toString(person.getPresentoId());
				if (person.getEmployment().isEmployed(TimestampUtil.toTimestamp(dateFrom))) {
					monteur.type = EMPTYPE_INTERNAL;
				}
			}
		}
		
		for (final AbstractCode monteurSir : AbstractCode.getInstances(LWMonteurSirCode.class)) {
			final Monteur monteur = getOrCreate(monteurSir.getShortText());
			if (monteur == null)
				continue;
			monteur.fullNameLogi = monteurSir.getLongText();
			monteur.codeLogiware = (String) monteurSir.getKey();
		}

		final ArrayList<Monteur> monteurList = new ArrayList<Monteur>();
		for (final MasterMitarbeiter person : getDispoMonteure(server)) {
			if (MITARBEITER_MONTEUR.equals(person.getEmploymentRole())) {
				Monteur monteur = getOrCreate(person.getShortName());
				if (monteur == null) {
					monteur = new Monteur();
					monteur.shortName = Strings.isEmpty(person.getShortName())
					 					? "" : person.getShortName();
					monteurList.add(monteur);
				}
				monteur.firstNameDispo = person.getFirstName();
				monteur.lastNameDispo = person.getLastName();
				monteur.codeDispo = person.getId().toString();
				monteur.type = person.getEmploymentType();
				monteur.gse = person.getGSE();
			}
		}
	
		final PacketStatisticResponse response = PacketStatisticResponse.create(packet);
		final ArrayList<DataPoint> dataList = new ArrayList<DataPoint>();
		final CSVFile csvFile = new CSVFile();
		CSVLine csvLine = csvFile.addLine();
		csvLine.setColumn(0, "Kurzzeichen");
		csvLine.setColumn(1, "Name (Presento)");
		csvLine.setColumn(2, "Vorname (Presento)");
		csvLine.setColumn(3, "Code (Presento)");
		csvLine.setColumn(4, "Name (Logiware)");
		csvLine.setColumn(5, "Code (Logiware)");
		csvLine.setColumn(6, "Name (Dispo)");
		csvLine.setColumn(7, "Vorname (Dispo)");
		csvLine.setColumn(8, "Code (Dispo)");
		csvLine.setColumn(9, "Art");
		csvLine.setColumn(10, "GSE");
		monteurList.addAll(monteurMap.values());
		Collections.sort(monteurList, MONTEUR_COMPARATOR);
		for (final Monteur monteur : monteurList) {
			csvLine = csvFile.addLine();
			csvLine.setColumn(0, monteur.shortName);
			csvLine.setColumn(1, monteur.lastNamePresento);
			csvLine.setColumn(2, monteur.firstNamePresento);
			csvLine.setColumn(3, monteur.codePresento);
			csvLine.setColumn(4, monteur.fullNameLogi);
			csvLine.setColumn(5, monteur.codeLogiware);
			csvLine.setColumn(6, monteur.lastNameDispo);
			csvLine.setColumn(7, monteur.firstNameDispo);
			csvLine.setColumn(8, monteur.codeDispo);
			csvLine.setColumn(9, AbstractCode.getText(monteur.type, null));
			csvLine.setColumn(10, monteur.gse);
			if (!Strings.isEmpty(monteur.codePresento)) {
				final DataPoint p = new DataPoint("Presento\n"+monteur.shortName, 1);
				dataList.add(p);
			}
			if (!Strings.isEmpty(monteur.codeLogiware)) {
				final DataPoint p = new DataPoint("Logiware\n"+monteur.shortName, 1);
				dataList.add(p);
			}
			if (!Strings.isEmpty(monteur.codeDispo)) {
				final DataPoint p = new DataPoint("Dispo\n"+monteur.shortName, 1);
				dataList.add(p);
			}
		}

		// --- values
		double[] data = getValues(dataList);
		DataPoint.round(data);
		response.setDataSeries(new double[][] { data });

		// --- labels
		String[] labels = getLabels(dataList);
		response.setLabels(labels);
		response.setReportTitle("Monteure");
		response.setChartTitle("Auswertung Monteure\n(Presento, Logiware, Dispo)");
		response.setXAxisLabel("");
		response.setYAxisLabel("Monteur");

		// --- attachments
		response.addAttachment("Monteurliste.csv", csvFile.toBinary(null));
		
		return response;
	}

	private Monteur getOrCreate(final String shortName) {
		final Monteur result;
		if (Strings.isEmpty(shortName) || Strings.equalsIgnoreCase("MONT", shortName)) {
			result = null;
		} else {
			final String upper = shortName.toUpperCase();
			Monteur monteur = monteurMap.get(upper);
			if (monteur == null) {
				monteur = new Monteur();
				monteur.shortName = upper;
				monteurMap.put(upper, monteur);
			}
			result = monteur;
		}
		return result;
	}

	@SuppressWarnings("unchecked")
	public static Collection<MasterMitarbeiter> getDispoMonteure(final DSPServer server) {
		final ModelContext context = server.getGlobalContext();
		return (Collection<MasterMitarbeiter>) ((Collection) context.getModelList(MasterMitarbeiter.class));
	}
	
	public static Collection<PresentoPerson> getPresentoMonteure(final DSPServer server) {
		final PresentoCache cache = server.getPresentoCache();
		final Iterator<PresentoPerson> i = cache.iterator();
		final ArrayList<PresentoPerson> result = new ArrayList<>();
		while (i.hasNext()) {
			result.add(i.next());
		}
		return result;
		
		
	}

}
