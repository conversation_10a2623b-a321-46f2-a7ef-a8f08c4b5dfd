package ch.eisenring.dispo.server.service.statistics;

import static ch.eisenring.dispo.server.DSPServerConstants.STATE_RCFG_STAT_AUSLAST_KW;
import static ch.eisenring.dispo.server.service.statistics.AuslastungObjektTypBewertung.LABEL_EXCLUDE;
import static ch.eisenring.dispo.server.service.statistics.AuslastungObjektTypBewertung.LABEL_NOT_FOUND;

import java.sql.Connection;
import java.sql.SQLException;

import ch.eisenring.core.codetype.AbstractCode;
import ch.eisenring.core.collections.Collection;
import ch.eisenring.core.collections.List;
import ch.eisenring.core.collections.Map;
import ch.eisenring.core.collections.impl.ArrayList;
import ch.eisenring.core.collections.impl.HashMap;
import ch.eisenring.core.datatypes.date.TimestampUtil;
import ch.eisenring.core.datatypes.strings.Strings;
import ch.eisenring.core.logging.Logger;
import ch.eisenring.core.logging.Logger.LogLevel;
import ch.eisenring.dispo.server.DSPServer;
import ch.eisenring.jdbc.JDBCResultSet;
import ch.eisenring.logiware.code.soft.LWObjektBetreuerCode;
import ch.eisenring.statistics.shared.codetables.STDateGranularityCode;
import ch.eisenring.statistics.shared.data.DataPoint;
import ch.eisenring.statistics.shared.network.PacketStatisticRequest;
import ch.eisenring.statistics.shared.network.PacketStatisticResponse;

public class AuslastungObjektTypObjektBetreuerKW extends AuslastungObjektTypObjektBetreuer {

	public AuslastungObjektTypObjektBetreuerKW(final DSPServer server) {
		super(server);
	}

	@Override
	@SuppressWarnings("unchecked")
	public Collection<PacketStatisticResponse> createResponses(final PacketStatisticRequest packet) {
		final List<PacketStatisticResponse> result = new ArrayList<PacketStatisticResponse>();
		final Collection<LWObjektBetreuerCode> types = (Collection) AbstractCode.getInstances(LWObjektBetreuerCode.class);
		for (LWObjektBetreuerCode type : types) {
			if (AbstractCode.isNull(type))
				continue;
			final PacketStatisticResponse response = createResponse(packet, type); 
			if (response.getDataSeries()[0].length > 0) {
				// only add responses with data
				result.add(response);
			}
		}
		return result;
	}

	@Override
	public PacketStatisticResponse createResponse(final PacketStatisticRequest packet) {
		throw new UnsupportedOperationException("createResponse() not supported");
	}

	public PacketStatisticResponse createResponse(final PacketStatisticRequest packet, final LWObjektBetreuerCode objektBetreuer) {
		PacketStatisticResponse result = null;
		final String betreuerName = objektBetreuer.getText();
		final String sql = createQuery(packet, getKWStartDatumNotNull(packet.getDateFrom()), getKWEndDatumNotNull(packet.getDateUpto()));
		
		Logger.log(LogLevel.TRACE, "SQL: "+sql);
		final AuslastungObjektTypBewertung bewertung = new AuslastungObjektTypBewertung(server, STATE_RCFG_STAT_AUSLAST_KW);
		
		// --- execute query
		final Connection connection = connectToLogiware();
		if (null!=connection) {
			try {
				final JDBCResultSet resultSet = doFetchQuery(sql);
				final Map<String, DataPoint> dataMap = new HashMap<>();
				while (resultSet.next()) {
					final StatisticAuftrag auftrag = new StatisticAuftrag();
					auftrag.loadRow(resultSet);
					final String label = bewertung.getLabel(auftrag);
					if (LABEL_EXCLUDE.equals(label) || LABEL_NOT_FOUND.equals(label)) {
						continue;
					}
					final String betreuer = AbstractCode.isNull(auftrag.objektBetreuer)
								? AbstractCode.getByKey("530", LWObjektBetreuerCode.class).getText()	// 530 = "Noch keine Zuordnung"
								: auftrag.objektBetreuer.toString();
					if (betreuer.equals(betreuerName)) {			
						final String kwString = TimestampUtil.KWDATE7.format(auftrag.wunschDatum);
						final String key = Strings.concat(label, "\n", kwString);
						DataPoint p = dataMap.get(key);
						if (p == null) {
							p = new DataPoint(key, 0);
							dataMap.put(key, p);
						}
						p.addValue(1);
					}
				}
				resultSet.close();

				final List<DataPoint> list = new ArrayList<DataPoint>(dataMap.values());
				addMissingCategoryValues(list, 0);

				result = PacketStatisticResponse.create(packet);
				result.setReportTitle(betreuerName);
				
				// --- chart title
				String title = STDateGranularityCode.getDateRangeText(result);
				title = packet.getType().getText()+" ("+betreuerName+")\n("+title+")";
				result.setChartTitle(title);

				// --- values
				double[] data = getValues(list);
				result.setDataSeries(new double[][] { data });

				// --- labels
				String[] labels = getLabels(list);
				result.setLabels(labels);
			} catch (SQLException e) {	 
				Logger.log(LogLevel.ERROR, e);
			} finally {
				close();
			}
		}
		return result;
	}
	
}
