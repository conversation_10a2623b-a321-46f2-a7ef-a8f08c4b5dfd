<?xml version="1.0" encoding="UTF-8"?>
<jasperReport xmlns="http://jasperreports.sourceforge.net/jasperreports" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://jasperreports.sourceforge.net/jasperreports http://jasperreports.sourceforge.net/xsd/jasperreport.xsd" name="IV_A4_8P" columnCount="2" pageWidth="595" pageHeight="842" columnWidth="261" columnSpacing="4" leftMargin="34" rightMargin="34" topMargin="25" bottomMargin="25" uuid="fc0e25da-18b2-4e1b-bdfb-d88f76254b10">
	<property name="ireport.scriptlethandling" value="0"/>
	<property name="ireport.encoding" value="UTF-8"/>
	<property name="ireport.zoom" value="1.0"/>
	<property name="ireport.x" value="0"/>
	<property name="ireport.y" value="0"/>
	<import value="net.sf.jasperreports.engine.*"/>
	<import value="java.util.*"/>
	<import value="net.sf.jasperreports.engine.data.*"/>
	<parameter name="TITLE" class="java.lang.String" isForPrompting="false"/>
	<parameter name="DRUCKDATUM" class="java.lang.String" isForPrompting="false"/>
	<parameter name="USER" class="java.lang.String" isForPrompting="false"/>
	<field name="documentImage" class="java.awt.Image"/>
	<field name="documentName" class="java.lang.String"/>
	<field name="documentSize" class="java.lang.String"/>
	<field name="documentDate" class="java.lang.String"/>
	<field name="documentText" class="java.lang.String"/>
	<background>
		<band splitType="Stretch"/>
	</background>
	<title>
		<band splitType="Stretch"/>
	</title>
	<pageHeader>
		<band height="12" splitType="Prevent">
			<textField isBlankWhenNull="false">
				<reportElement uuid="0c0ff854-7b91-4b4f-ae79-f9461b67cbf0" key="PrintedBy" x="0" y="0" width="527" height="12" forecolor="#999999"/>
				<textElement textAlignment="Right" verticalAlignment="Top">
					<font fontName="Avenir LT 35 Light" size="8" isBold="true" isItalic="true" pdfFontName="Helvetica-BoldOblique" isPdfEmbedded="true"/>
				</textElement>
				<textFieldExpression><![CDATA["Gedruckt am " + $P{DRUCKDATUM} + " von " + $P{USER}]]></textFieldExpression>
			</textField>
		</band>
	</pageHeader>
	<columnHeader>
		<band splitType="Prevent"/>
	</columnHeader>
	<detail>
		<band height="195" splitType="Stretch">
			<image scaleImage="RetainShape" hAlign="Center" vAlign="Middle">
				<reportElement uuid="37bff892-019a-4eb5-95a3-ca7412efa6ab" key="DocumentImage" mode="Transparent" x="0" y="0" width="260" height="183"/>
				<imageExpression><![CDATA[$F{documentImage}]]></imageExpression>
			</image>
			<textField isBlankWhenNull="false">
				<reportElement uuid="b4681164-f6a8-4906-91eb-2d3b6f89667f" key="DocumentName" x="0" y="183" width="260" height="12"/>
				<textElement textAlignment="Center" verticalAlignment="Top">
					<font fontName="Avenir LT 35 Light" size="5" isBold="true" pdfFontName="Helvetica-Bold" isPdfEmbedded="true"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{documentName}]]></textFieldExpression>
			</textField>
			<textField pattern="" isBlankWhenNull="false">
				<reportElement uuid="5368cce8-56f6-4a9f-954a-ecac043b6ab5" key="DocumentProperties" mode="Transparent" x="0" y="183" width="260" height="12"/>
				<textElement textAlignment="Center" verticalAlignment="Bottom">
					<font fontName="Avenir LT 35 Light" size="5" isPdfEmbedded="true"/>
				</textElement>
				<textFieldExpression><![CDATA["Grösse " + $F{documentSize} + ", Version vom " + $F{documentDate}]]></textFieldExpression>
			</textField>
		</band>
	</detail>
	<columnFooter>
		<band splitType="Stretch"/>
	</columnFooter>
	<pageFooter>
		<band splitType="Stretch"/>
	</pageFooter>
	<summary>
		<band splitType="Stretch"/>
	</summary>
</jasperReport>
