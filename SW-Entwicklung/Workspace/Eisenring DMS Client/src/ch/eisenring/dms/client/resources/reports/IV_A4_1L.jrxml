<?xml version="1.0" encoding="UTF-8"?>
<jasperReport xmlns="http://jasperreports.sourceforge.net/jasperreports" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://jasperreports.sourceforge.net/jasperreports http://jasperreports.sourceforge.net/xsd/jasperreport.xsd" name="IV_A4_1L" pageWidth="842" pageHeight="595" orientation="Landscape" columnWidth="774" leftMargin="34" rightMargin="34" topMargin="25" bottomMargin="25" uuid="291ee9d2-4456-418f-890c-4a4c1e93342d">
	<property name="ireport.scriptlethandling" value="0"/>
	<property name="ireport.encoding" value="UTF-8"/>
	<property name="ireport.zoom" value="1.0"/>
	<property name="ireport.x" value="0"/>
	<property name="ireport.y" value="0"/>
	<import value="net.sf.jasperreports.engine.*"/>
	<import value="java.util.*"/>
	<import value="net.sf.jasperreports.engine.data.*"/>
	<parameter name="TITLE" class="java.lang.String" isForPrompting="false"/>
	<parameter name="DRUCKDATUM" class="java.lang.String" isForPrompting="false"/>
	<parameter name="USER" class="java.lang.String" isForPrompting="false"/>
	<field name="documentImage" class="java.awt.Image"/>
	<field name="documentName" class="java.lang.String"/>
	<field name="documentSize" class="java.lang.String"/>
	<field name="documentDate" class="java.lang.String"/>
	<field name="documentText" class="java.lang.String"/>
	<background>
		<band splitType="Stretch"/>
	</background>
	<title>
		<band splitType="Stretch"/>
	</title>
	<pageHeader>
		<band height="12" splitType="Prevent">
			<textField isBlankWhenNull="false">
				<reportElement uuid="ea267858-e364-4f81-b3f2-3f037e0f901b" key="PrintedBy" x="0" y="0" width="773" height="12" forecolor="#999999"/>
				<textElement textAlignment="Right">
					<font fontName="Avenir LT 35 Light" size="8" isBold="true" isItalic="true" pdfFontName="Helvetica-BoldOblique" isPdfEmbedded="true"/>
				</textElement>
				<textFieldExpression><![CDATA["Gedruckt am " + $P{DRUCKDATUM} + " von " + $P{USER}]]></textFieldExpression>
			</textField>
		</band>
	</pageHeader>
	<columnHeader>
		<band splitType="Prevent"/>
	</columnHeader>
	<detail>
		<band height="533" splitType="Stretch">
			<image scaleImage="RetainShape" hAlign="Center" vAlign="Middle">
				<reportElement uuid="25e3dcfd-7019-4232-ad3a-3b9fc2ac2c25" key="DocumentImage" mode="Transparent" x="0" y="0" width="773" height="512"/>
				<imageExpression><![CDATA[$F{documentImage}]]></imageExpression>
			</image>
			<textField isBlankWhenNull="false">
				<reportElement uuid="c1bf64ea-e43f-4015-a3cd-0f8520df9578" key="DocumentName" x="0" y="512" width="773" height="20"/>
				<textElement textAlignment="Center" verticalAlignment="Top">
					<font fontName="Avenir LT 35 Light" size="8" isBold="true" pdfFontName="Helvetica-Bold" isPdfEmbedded="true"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{documentName}]]></textFieldExpression>
			</textField>
			<textField isBlankWhenNull="false">
				<reportElement uuid="a115c850-f008-40a3-bfb2-d6ee0d692b80" key="DocumentProperties" x="0" y="512" width="773" height="20"/>
				<textElement textAlignment="Center" verticalAlignment="Bottom">
					<font fontName="Avenir LT 35 Light" size="8" isPdfEmbedded="true"/>
				</textElement>
				<textFieldExpression><![CDATA["Grösse " + $F{documentSize} + ", Version vom " + $F{documentDate}]]></textFieldExpression>
			</textField>
		</band>
	</detail>
	<columnFooter>
		<band splitType="Stretch"/>
	</columnFooter>
	<pageFooter>
		<band splitType="Stretch"/>
	</pageFooter>
	<summary>
		<band splitType="Stretch"/>
	</summary>
</jasperReport>
