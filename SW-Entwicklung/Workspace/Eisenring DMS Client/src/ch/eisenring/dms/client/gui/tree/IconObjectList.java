package ch.eisenring.dms.client.gui.tree;

import javax.swing.JList;

import ch.eisenring.core.observable.ObserverNotificationPolicy;
import ch.eisenring.dms.client.DMSClient;
import ch.eisenring.dms.service.api.DMSObject;

@SuppressWarnings("serial")
public class IconObjectList extends JList<DMSObject> {

	private final IconSizeObserver iconSizeObserver = new IconSizeObserver(this);

	public IconObjectList(final DMSClient client) {
		client.ICON_SIZE.addObserver(iconSizeObserver, ObserverNotificationPolicy.EDT_ASYNCHRONOUS);
		client.LABEL_FORMAT.addObserver(iconSizeObserver, ObserverNotificationPolicy.EDT_ASYNCHRONOUS);
		setCellRenderer(new ListElementRenderer(client));
	}

}
