package ch.eisenring.core.io;

import java.io.IOException;
import java.io.InputStream;
import java.io.OutputStream;

import ch.eisenring.core.collections.Collection;
import ch.eisenring.core.collections.impl.ArrayList;
import ch.eisenring.core.datatypes.primitives.Primitives;
import ch.eisenring.core.threading.Task;
import ch.eisenring.core.threading.ThreadPool;

/**
 * Multithreaded Stream copy implementation.
 *
 * Has a high resource cost (buffers, threads), but can perform stream
 * copy operations up to twice as fast as a naive implementation.
 */
final class StreamBurstCopy implements Task {

	final static int MAX_WAIT_MS = 128;
	
	final static int READER_FINISHED = 0x1;
	final static int WRITER_FINISHED = 0x2;
	final static int ERROR_OCCURED   = 0x8;
	
	final static int READER_ABORT = READER_FINISHED | ERROR_OCCURED;
	final static int WRITER_ABORT = WRITER_FINISHED | ERROR_OCCURED;

	static class Buffer {
		private final byte[] buffer;
		private final int offset;
		private final int size;
		private int length;
		
		public Buffer(final int size) {
			this(Primitives.newByteArray(size), 0, size);
		}

		public Buffer(final byte[] buffer, final int offset, final int size) {
			this.buffer = buffer;
			this.offset = offset;
			this.size = size;
		}

		public void reset() {
			this.length = 0;
		}
	
		/**
		 * Fills this buffer with as many bytes as possible
		 * from the stream. Return number of bytes read - same as
		 * InputStream.read(byte[], int, int).
		 */
		public int read(final InputStream input) throws IOException {
			final int result = input.read(buffer, offset, size);
			length = Math.max(0, result);
			return result;
		}

		/**
		 * Fills this buffer with the specified number of bytes,
		 * or the entire buffer (whichever is smaller).
		 */
		public int read(final InputStream input, final int maxSize) throws IOException {
			final int result = input.read(buffer, offset, Math.min(size, maxSize));
			length = Math.max(0, result);
			return result;
		}
		
		/**
		 * Writes the contents of this buffer to a stream.
		 */
		public void write(final OutputStream output) throws IOException {
			if (length <= 0)
				return;
			output.write(buffer, offset, length);
		}
		
		/**
		 * Creates multiple buffers and puts them into the list.
		 */
		public static void createBuffers(final Collection<Buffer> collection, final int count, final int size) {
			final byte[] array = Primitives.newByteArray(count * size);
			int offset = 0;
			for (int i=0; i<count; ++i) {
				final Buffer buffer = new Buffer(array, offset, size);
				collection.add(buffer);
				offset += size;
			}
		}
	}

	final InputStream source;
	final OutputStream sink;
	
	final ArrayList<Buffer> fullList = new ArrayList<>(4);
	final ArrayList<Buffer> emptyList = new ArrayList<>(4);
	int status;
	Throwable error;
	
	StreamBurstCopy(final InputStream source, final OutputStream sink) {
		this.source = source;
		this.sink = sink;
		// create the buffers (we use 4 instead of 2 for decoupling)
		Buffer.createBuffers(emptyList, 4, 1 << 16);
	}

	public static long copy(final InputStream source, final OutputStream sink) throws IOException {
		final StreamBurstCopy instance = new StreamBurstCopy(source, sink);
		return instance.copy(-1);
	}

	public static long copyFully(final InputStream source, final OutputStream sink, final long size) throws IOException {
		final StreamBurstCopy instance = new StreamBurstCopy(source, sink);
		return instance.copy(size);
	}

	/**
	 * Records the error cause (if no other cause has already
	 * been recorded, only the first cause is reported to
	 * the caller) and sets the ERROR_OCCURED flag.
	 */
	synchronized void terminate(final Throwable cause) {
		status |= ERROR_OCCURED;
		if (this.error == null)
			this.error = cause;
		notifyAll();
	}

	/**
	 * Performs the actual copy operation. Attempts to emulate a single threaded
	 * exception model (exceptions from multiple threads will be delegated to the
	 * calling thread).
	 */
	long copy(final long copySize) throws IOException {
		// start writer thread (the calling thread works as the reader thread)
		synchronized (this) {
			status = 0;
		}
		ThreadPool.DEFAULT.start(this);

		// read loop
		long result = 0;
		boolean suspect = false;
		try {
Loop:		while (copySize < 0 || result < copySize) {
				final Buffer buffer;
				synchronized (this) {
					while (true) {
						if ((status & ERROR_OCCURED) != 0) { 
							break Loop;
						} else if (!emptyList.isEmpty()) {
							buffer = emptyList.remove(0);
							break;
						}
						try {
							wait(MAX_WAIT_MS);
						} catch (final InterruptedException e) {
							if (error == null)
								error = e;
							status |= ERROR_OCCURED;
							notifyAll();
							break Loop;
						}
					}
				}
				if (buffer == null)
					break;
				final int l;
				try {
					// determine how many bytes to copy in this block (at maximum)
					final int readSize = copySize < 0 ? Integer.MAX_VALUE
							: (int) Math.min(copySize - result, Integer.MAX_VALUE);
					l = buffer.read(source, readSize);
				} catch (final IOException e) {
					terminate(e);
					break;
				}
				synchronized (this) {
					if (l <= 0) {
						emptyList.add(buffer);
						notifyAll();
						if (l == -1 || suspect)
							break;
						suspect = true;
					} else {
						result += buffer.length;
						fullList.add(buffer);
						notifyAll();
						suspect = false;
					}
				}
			}
		} catch (final Exception e) {
			terminate(e);
		} finally {
			synchronized (this) {
				status |= READER_FINISHED;
			}
		}
		// we still need to wait for the writer to finish/abort
		while (true) {
			synchronized (this) {
				if ((status & WRITER_FINISHED) != 0)
					break;
				try {
					wait(MAX_WAIT_MS);
				} catch (final InterruptedException e) {
					terminate(e);
				}
			}
		}
		synchronized (this) {
			if (error != null)
				throw new IOException(error.getMessage(), error);
		}
		return result;
	}

	// --------------------------------------------------------------
	// ---
	// --- Implement the write thread (Runnable interface)
	// ---
	// --------------------------------------------------------------
	@Override
	public void run() {
		try {
Loop:		while (true) {
				// get next buffer to be written (wait if required)
				final Buffer buffer;
				synchronized (this) {
					while (true) {
						if ((status & ERROR_OCCURED) != 0) {
							break Loop;
						} else if (!fullList.isEmpty()) {
							buffer = fullList.remove(0);
							break;
						} else if ((status & READER_FINISHED) != 0) {
							break Loop;
						}
						try {
							wait(MAX_WAIT_MS);
						} catch (final InterruptedException e) {
							if (error == null)
								error = e;
							status |= ERROR_OCCURED;
							notifyAll();
							break Loop;
						}
					}
				}
				if (buffer == null)
					break;
				try {
					buffer.write(sink);
				} finally {
					synchronized (this) {
						emptyList.add(buffer);
						notifyAll();
					}
				}
			}
		} catch (final Exception e) {
			terminate(e);
		} finally {
			synchronized (this) {
				status |= WRITER_FINISHED;
				notifyAll();
			}
		}
	}

	// --------------------------------------------------------------
	// ---
	// --- Task implementation
	// ---
	// --------------------------------------------------------------
	@Override
	public String getName() {
		return "StreamBurstCopy";
	}

	@Override
	public int getPriority() {
		return Thread.NORM_PRIORITY + 2;
	}

	@Override
	public boolean isDaemon() {
		return false;
	}

}
