package ch.eisenring.core.io;

import java.awt.Dimension;
import java.awt.Point;
import java.awt.Rectangle;
import java.io.ByteArrayInputStream;
import java.io.IOException;
import java.io.ObjectInputStream;
import java.io.ObjectOutputStream;
import java.io.Serializable;
import java.lang.reflect.Constructor;
import java.util.Collections;
import java.util.Comparator;
import java.util.Date;
import java.util.Iterator;

import ch.eisenring.core.codetype.AbstractCode;
import ch.eisenring.core.collections.Collection;
import ch.eisenring.core.collections.Lookup;
import ch.eisenring.core.collections.Map;
import ch.eisenring.core.collections.impl.ArrayList;
import ch.eisenring.core.collections.impl.ArraySet;
import ch.eisenring.core.collections.impl.AtomicArrayLookup;
import ch.eisenring.core.collections.impl.HashMap;
import ch.eisenring.core.datatypes.primitives.Primitives;
import ch.eisenring.core.datatypes.strings.Strings;
import ch.eisenring.core.io.binary.BinaryHolder;
import ch.eisenring.core.io.memory.MemoryOutputStream;

abstract class StreamableObjectType {

	private final static ArrayList<StreamableObjectType> INSTANCES = new ArrayList<>();

	final static StreamableObjectType NULL = new StreamableObjectType(null, 0) {
		@Override
		Object read(final StreamReader reader) throws IOException {
			return null;
		}
		@Override
		void write(final StreamWriterBase writer, final Object value) throws IOException {
			writer.writeObjectId(this);
		}
	};

	final static StreamableObjectType BYTE = new StreamableObjectType(Byte.class, 1,  9) {
		@Override
		Object read(final StreamReader reader) throws IOException {
			return Byte.valueOf(reader.readByte());
		}
		@Override
		void write(final StreamWriterBase writer, final Object value) throws IOException {
			writer.writeObjectId(this);
			writer.writeByte(((Byte) value).byteValue());
		}
	};
	
	final static StreamableObjectType SHORT = new StreamableObjectType(Short.class, 2, 10) {
		@Override
		Object read(final StreamReader reader) throws IOException {
			return Short.valueOf(reader.readShort());
		}
		@Override
		void write(final StreamWriterBase writer, final Object value) throws IOException {
			writer.writeObjectId(this);
			writer.writeShort(((Short) value).shortValue());
		}
	};

	final static StreamableObjectType INT = new StreamableObjectType(Integer.class, 3,  1) {
		@Override
		Object read(final StreamReader reader) throws IOException {
			return Integer.valueOf(reader.readInt());
		}
		@Override
		void write(final StreamWriterBase writer, final Object value) throws IOException {
			writer.writeObjectId(this);
			writer.writeInt(((Integer) value).intValue());
		}
	};

	final static StreamableObjectType LONG = new StreamableObjectType(Long.class, 4,  2) {
		@Override
		Object read(final StreamReader reader) throws IOException {
			return Long.valueOf(reader.readLong());
		}
		@Override
		void write(final StreamWriterBase writer, final Object value) throws IOException {
			writer.writeObjectId(this);
			writer.writeLong(((Long) value).longValue());
		}
	};

	final static StreamableObjectType FLOAT = new StreamableObjectType(Float.class, 5, 11) {
		@Override
		Object read(final StreamReader reader) throws IOException {
			return Primitives.valueOf(reader.readFloat());
		}
		@Override
		void write(final StreamWriterBase writer, final Object value) throws IOException {
			writer.writeObjectId(this);
			writer.writeFloat(((Float) value).floatValue());
		}
	};

	final static StreamableObjectType DOUBLE = new StreamableObjectType(Double.class, 6, 12) {
		@Override
		Object read(final StreamReader reader) throws IOException {
			return Primitives.valueOf(reader.readDouble());
		}
		@Override
		void write(final StreamWriterBase writer, final Object value) throws IOException {
			writer.writeObjectId(this);
			writer.writeDouble(((Double) value).doubleValue());
		}
	};

	final static StreamableObjectType CODE = new StreamableObjectType(AbstractCode.class, 7, 4) {
		@SuppressWarnings("unchecked")
		@Override
		Object read(final StreamReader reader) throws IOException {
			final Class<? extends AbstractCode> typeClass = (Class<? extends AbstractCode>) reader.readClass();
			return reader.readCode(typeClass);
		}
		@Override
		void write(final StreamWriterBase writer, final Object value) throws IOException {
			writer.writeObjectId(this);
			final AbstractCode code = (AbstractCode) value;
			writer.writeClass(code.getTypeClass());
			writer.writeCode(code);
		}
	};

	final static StreamableObjectType FALSE = new StreamableObjectType(Boolean.class, 8, 7) {
		@Override
		Object read(final StreamReader reader) throws IOException {
			return Boolean.FALSE;
		}
		@Override
		void write(final StreamWriterBase writer, final Object value) throws IOException {
			writer.writeObjectId(Boolean.TRUE.equals(value) ? TRUE : this);
		}
	};

	final static StreamableObjectType TRUE = new StreamableObjectType(Boolean.class, 9, 8) {
		@Override
		Object read(final StreamReader reader) throws IOException {
			return Boolean.TRUE;
		}
		@Override
		void write(final StreamWriterBase writer, final Object value) throws IOException {
			writer.writeObjectId(Boolean.TRUE.equals(value) ? this : FALSE);
		}
	};

	final static StreamableObjectType BINARY = new StreamableObjectType(byte[].class, 10, 5) {
		@Override
		Object read(final StreamReader reader) throws IOException {
			return reader.readBinary();
		}
		@Override
		void write(final StreamWriterBase writer, final Object value) throws IOException {
			writer.writeObjectId(this);
			writer.writeBinary((byte[]) value);
		}
	};

	final static StreamableObjectType DATE = new StreamableObjectType(Date.class, 11, 6) {
		@Override
		Object read(final StreamReader reader) throws IOException {
			return reader.readDate();
		}
		@Override
		void write(final StreamWriterBase writer, final Object value) throws IOException {
			writer.writeObjectId(this);
			writer.writeDate((Date) value);
		}
	};
	
	final static StreamableObjectType STRING = new StreamableObjectType(String.class, 12, 3) {
		@Override
		Object read(final StreamReader reader) throws IOException {
			return reader.readString();
		}
		@Override
		void write(final StreamWriterBase writer, final Object value) throws IOException {
			writer.writeObjectId(this);
			writer.writeString((String) value);
		}
	};

	final static StreamableObjectType CHAR = new StreamableObjectType(Character.class, 13) {
		@Override
		Object read(final StreamReader reader) throws IOException {
			return Character.valueOf(reader.readChar());
		}
		@Override
		void write(final StreamWriterBase writer, final Object value) throws IOException {
			writer.writeObjectId(this);
			writer.writeChar(((Character) value).charValue());
		}
	};

	final static StreamableObjectType CLASS = new StreamableObjectType(Class.class, 14) {
		@Override
		Object read(final StreamReader reader) throws IOException {
			return reader.readClass();
		}
		@Override
		void write(final StreamWriterBase writer, final Object value) throws IOException {
			writer.writeObjectId(this);
			writer.writeClass((Class<?>) value);
		}
	};

	final static StreamableObjectType DIMENSION = new StreamableObjectType(Dimension.class, 15) {
		@Override
		Object read(final StreamReader reader) throws IOException {
			return new Dimension(reader.readInt(), reader.readInt());
		}
		@Override
		void write(final StreamWriterBase writer, final Object value) throws IOException {
			writer.writeObjectId(this);
			writer.writeInt(((Dimension) value).width);
			writer.writeInt(((Dimension) value).height);
		}
	};

	final static StreamableObjectType POINT = new StreamableObjectType(Point.class, 16) {
		@Override
		Object read(final StreamReader reader) throws IOException {
			return new Point(reader.readInt(), reader.readInt());
		}
		@Override
		void write(final StreamWriterBase writer, final Object value) throws IOException {
			writer.writeObjectId(this);
			writer.writeInt(((Point) value).x);
			writer.writeInt(((Point) value).y);
		}
	};

	final static StreamableObjectType RECTANGLE = new StreamableObjectType(Rectangle.class, 17) {
		@Override
		Object read(final StreamReader reader) throws IOException {
			return new Rectangle(reader.readInt(), reader.readInt(), reader.readInt(), reader.readInt());
		}
		@Override
		void write(final StreamWriterBase writer, final Object value) throws IOException {
			writer.writeObjectId(this);
			final Rectangle r = (Rectangle) value;
			writer.writeInt(r.x);
			writer.writeInt(r.y);
			writer.writeInt(r.width);
			writer.writeInt(r.height);
		}
	};

	final static StreamableObjectType STRING_ARRAY = new StreamableObjectType(String[].class, 18) {
		@SuppressWarnings("deprecation")
		@Override
		Object read(final StreamReader reader) throws IOException {
			final int l = reader.readInt();
			if (l < 0) {
				if (l == -1)
					return null;
				throw new IOException("invalid String[] encoding");
			} else {
				final String[] array;
				try {
					array = (String[]) Primitives.newArray(String.class, l);
				} catch (final OutOfMemoryError e) {
					throw new StreamException(Strings.concat(
						"failed to allocate String[", l, "]:", e.getMessage()));
				}
				for (int i=0; i<l; ++i) {
					array[i] = reader.readString();
				}
				return array;
			}
		}
		@SuppressWarnings("deprecation")
		@Override
		void write(final StreamWriterBase writer, final Object value) throws IOException {
			writer.writeObjectId(this);
			final String[] array = (String[]) value;
			writer.writeInt(array.length);
			for (int i=0; i<array.length; ++i) {
				writer.writeString(array[i]);
			}
		}
	};

	final static StreamableObjectType OBJID_DOUBLE_ARRAY = new StreamableObjectType(double[].class, 19) {
		@SuppressWarnings("deprecation")
		@Override
		Object read(final StreamReader reader) throws IOException {
			final int l = reader.readInt();
			if (l < 0) {
				if (l == -1)
					return null;
				throw new IOException("invalid double[] encoding");
			}
			final double[] array;
			try {
				array = Primitives.newDoubleArray(l);
			} catch (final OutOfMemoryError e) {
				throw new StreamException(Strings.concat(
					"failed to allocate double[", l, "]:", e.getMessage()));
			}
			for (int i=0; i<l; ++i) {
				array[i] = reader.readDouble();
			}
			return array;
		}
		@SuppressWarnings("deprecation")
		@Override
		void write(final StreamWriterBase writer, final Object value) throws IOException {
			writer.writeObjectId(this);
			final double[] array = (double[]) value;
			writer.writeInt(array.length);
			for (int i=0; i<array.length; ++i) {
				writer.writeDouble(array[i]);
			}
		}
	};

	@SuppressWarnings("deprecation")
	final static StreamableObjectType STREAMABLE = new StreamableObjectType(AutoStreamable.class, 20) {
		@SuppressWarnings("unchecked")
		@Override
		Object read(final StreamReader reader) throws IOException {
			final Class<?> classRef;
			if ((((StreamReaderBase) reader).OPTIONS & 1) == 0) {
				final String className = reader.readString();
				try {
					classRef = Primitives.classForName(className);
				} catch (final ClassNotFoundException e) {
					throw new IOException(e.getMessage(), e);
				}
			} else {
				classRef = reader.readClass();
			}
			try {
				final Constructor<?> constructor = classRef.getDeclaredConstructor(Primitives.EMPTY_CLASS_ARRAY);
				if (!constructor.isAccessible())
					constructor.setAccessible(true);
				final AutoStreamable object = (AutoStreamable) constructor.newInstance();
				final StreamableDescription description = StreamableDescription.getDescription(object.getClass());
				description.read(reader, object);
				if (object instanceof Streamable) {
					((Streamable) object).read(reader);
				}
				return object;
			} catch (final NoSuchMethodException e) {
				throw new StreamException(Strings.concat("no default constructor for Streamable '", classRef.getName(), "' found"), e);
			} catch (final IOException e) {
				throw e;
			} catch (final Exception e) {
				throw new IOException(e.getMessage(), e);
			}
		}
		@Override
		void write(final StreamWriterBase writer, final Object value) throws IOException {
			writer.writeObjectId(this);
			if ((writer.OPTIONS & 1) == 0) {
				writer.writeString(value.getClass().getName());
			} else {
				writer.writeClass(value.getClass());
			}
			StreamableDescription description = StreamableDescription.getDescription(value.getClass());
			description.write(writer, value);
			if (value instanceof Streamable) {
				((Streamable) value).write(writer);
			}
		}
	};

	final static StreamableObjectType FLOAT_ARRAY = new StreamableObjectType(float[].class, 21) {
		@Override
		Object read(final StreamReader reader) throws IOException {
			final int length = reader.readInt();
			final float[] array = Primitives.newFloatArray(length);
			for (int i=0; i<length; ++i)
				array[i] = reader.readFloat();
			return array;
		}
		@Override
		void write(final StreamWriterBase writer, final Object value) throws IOException {
			writer.writeObjectId(this);
			final float[] array = (float[]) value;
			final int length = array.length;
			writer.writeInt(length);
			for (int i=0; i<length; ++i) 
				writer.writeFloat(array[i]);
		}
	};

	final static StreamableObjectType BOOLEAN_ARRAY = new StreamableObjectType(boolean[].class, 22) {
		@Override
		Object read(final StreamReader reader) throws IOException {
			final int length = reader.readInt();
			final boolean[] array = Primitives.newBooleanArray(length);
			for (int i=0; i<length; ++i)
				array[i] = reader.readBoolean();
			return array;
		}
		@Override
		void write(final StreamWriterBase writer, final Object value) throws IOException {
			writer.writeObjectId(this);
			final boolean[] array = (boolean[]) value;
			final int length = array.length;
			writer.writeInt(length);
			for (int i=0; i<length; ++i) 
				writer.writeBoolean(array[i]);
		}
	};

	final static StreamableObjectType SHORT_ARRAY = new StreamableObjectType(short[].class, 23) {
		@Override
		Object read(final StreamReader reader) throws IOException {
			final int length = reader.readInt();
			final short[] array = Primitives.newShortArray(length);
			for (int i=0; i<length; ++i)
				array[i] = reader.readShort();
			return array;
		}
		@Override
		void write(final StreamWriterBase writer, final Object value) throws IOException {
			writer.writeObjectId(this);
			final short[] array = (short[]) value;
			final int length = array.length;
			writer.writeInt(length);
			for (int i=0; i<length; ++i) 
				writer.writeShort(array[i]);
		}
	};

	final static StreamableObjectType CHAR_ARRAY = new StreamableObjectType(char[].class, 24) {
		@Override
		Object read(final StreamReader reader) throws IOException {
			final int length = reader.readInt();
			final char[] array = Primitives.newCharArray(length);
			for (int i=0; i<length; ++i)
				array[i] = reader.readChar();
			return array;
		}
		@Override
		void write(final StreamWriterBase writer, final Object value) throws IOException {
			writer.writeObjectId(this);
			final char[] array = (char[]) value;
			final int length = array.length;
			writer.writeInt(length);
			for (int i=0; i<length; ++i) 
				writer.writeChar(array[i]);
		}
	};

	final static StreamableObjectType INT_ARRAY = new StreamableObjectType(int[].class, 25) {
		@Override
		Object read(final StreamReader reader) throws IOException {
			final int length = reader.readInt();
			final int[] array = Primitives.newIntArray(length);
			for (int i=0; i<length; ++i)
				array[i] = reader.readInt();
			return array;
		}
		@Override
		void write(final StreamWriterBase writer, final Object value) throws IOException {
			writer.writeObjectId(this);
			final int[] array = (int[]) value;
			final int length = array.length;
			writer.writeInt(length);
			for (int i=0; i<length; ++i) 
				writer.writeInt(array[i]);
		}
	};

	final static StreamableObjectType LONG_ARRAY = new StreamableObjectType(long[].class, 26) {
		@Override
		Object read(final StreamReader reader) throws IOException {
			final int length = reader.readInt();
			final long[] array = Primitives.newLongArray(length);
			for (int i=0; i<length; ++i)
				array[i] = reader.readLong();
			return array;
		}
		@Override
		void write(final StreamWriterBase writer, final Object value) throws IOException {
			writer.writeObjectId(this);
			final long[] array = (long[]) value;
			final int length = array.length;
			writer.writeInt(length);
			for (int i=0; i<length; ++i) 
				writer.writeLong(array[i]);
		}
	};

	final static StreamableObjectType OBJECT_ARRAY = new StreamableObjectType(Object[].class, 27) {
		@Override
		Object read(final StreamReader reader) throws IOException {
			final Class<?> componentType = reader.readClass();
			final int length = reader.readInt();
			final Object[] array = (Object[]) Primitives.newArray(componentType, length);
			for (int i=0; i<length; ++i) {
				final Object object = reader.readObject();
				array[i] = object;
			}
			return array;
		}
		@Override
		void write(final StreamWriterBase writer, final Object value) throws IOException {
			writer.writeObjectId(this);
			final Object[] array = (Object[]) value;
			final int length = array.length;
			writer.writeClass(array.getClass());
			for (int i=0; i<length; ++i) {
				final Object object = array[i];
				writer.writeObject(object);
			}
		}
	};

	final static StreamableObjectType DOUBLE_ARRAY2 = new StreamableObjectType(double[][].class, 28) {
		@SuppressWarnings("deprecation")
		@Override
		Object read(final StreamReader reader) throws IOException {
			final int length = reader.readInt();
			final double[][] array = new double[length][];
			for (int i=0; i<length; ++i) {
				final double[] slice = (double[]) OBJID_DOUBLE_ARRAY.read(reader);
				array[i] = slice;
			}
			return array;
		}
		@SuppressWarnings("deprecation")
		@Override
		void write(final StreamWriterBase writer, final Object value) throws IOException {
			writer.writeObjectId(this);
			final double[][] array2 = (double[][]) value;
			final int count = array2.length;
			writer.writeInt(count);
			for (int i=0; i<count; ++i) {
				final double[] array = array2[i];
				if (array == null) {
					writer.writeInt(-1);
				} else {
					writer.writeInt(array.length);
					for (int j=0; j<array.length; ++j) {
						writer.writeDouble(array[j]);
					}
				}
			}
		}
	};

	final static StreamableObjectType SERIALIZABLE = new StreamableObjectType(Serializable.class, 29) {
		@Override
		Object read(final StreamReader reader) throws IOException {
			final byte[] objBytes = reader.readBinary();
			final ByteArrayInputStream byteIn = new ByteArrayInputStream(objBytes);
			final ObjectInputStream objIn = new ObjectInputStream(byteIn);
			try {
				return objIn.readObject();
			} catch (final ClassNotFoundException e) {
				throw new IOException(e.getMessage(), e);
			}
		}
		void write(final StreamWriterBase writer, final Object value) throws IOException {
			writer.writeObjectId(this);
			final MemoryOutputStream byteOut = new MemoryOutputStream();
			final ObjectOutputStream objOut = new ObjectOutputStream(byteOut);
			objOut.writeObject(value);
			final byte[] objBytes = byteOut.toByteArray();
			writer.writeBinary(objBytes);
		}
	};
	
	final static StreamableObjectType BINARY_HOLDER = new StreamableObjectType(BinaryHolder.class, 30) {
		@Override
		Object read(final StreamReader reader) throws IOException {
			return reader.readBinaryHolder();
		}
		@Override
		void write(final StreamWriterBase writer, final Object value) throws IOException {
			writer.writeObjectId(this);
			writer.writeBinaryHolder((BinaryHolder) value);
		}
	};

	final static StreamableObjectType LIST = new StreamableObjectType(java.util.List.class, 31) {
		@Override
		Object read(final StreamReader reader) throws IOException {
			final int count = reader.readInt();
			if (count < 0)
				return null;
			final ArrayList<Object> list = new ArrayList<>(count);
			for (int i=0; i<count; ++i) {
				final Object object = reader.readObject();
				list.add(object);
			}
			return list;
		}
		@Override
		void write(final StreamWriterBase writer, final Object value) throws IOException {
			writer.writeObjectId(this);
			final java.util.List<?> list = (java.util.List<?>) value;
			final int count = list == null ? -1 : list.size();
			writer.writeInt(count);
			for (int i=0; i<count; ++i) { 
				final Object object = list.get(i);
				writer.writeObject(object);
			}
		}
	};

	final static StreamableObjectType SET = new StreamableObjectType(java.util.Set.class, 32) {
		@Override
		Object read(final StreamReader reader) throws IOException {
			final int count = reader.readInt();
			if (count < 0)
				return null;
			final ArraySet<Object> set = new ArraySet<Object>(count);
			for (int i=0; i<count; ++i) {
				final Object object = reader.readObject();
				set.add(object);
			}
			return set;
		}
		@Override
		void write(final StreamWriterBase writer, final Object value) throws IOException {
			writer.writeObjectId(this);
			final java.util.Set<?> set = (java.util.Set<?>) value;
			final int count = set == null ? -1 : set.size();
			writer.writeInt(count);
			if (count > 0) {
				for (final Object object : set) { 
					writer.writeObject(object);
				}
			}
		}
	};

	final static StreamableObjectType MAP = new StreamableObjectType(java.util.Set.class, 33) {
		@Override
		Object read(final StreamReader reader) throws IOException {
			final int count = reader.readInt();
			if (count < 0)
				return null;
			final Map<Object, Object> map = new HashMap<>(count);
			for (int i=0; i<count; ++i) {
				final Object k = reader.readObject();
				final Object v = reader.readObject(); 
				map.put(k,  v);
			}
			return map;
		}
		@Override
		void write(final StreamWriterBase writer, final Object value) throws IOException {
			writer.writeObjectId(this);
			final java.util.Map<?, ?> map = (java.util.Map<?, ?>) value;
			final int count = map == null ? -1 : map.size();
			writer.writeInt(count);
			if (count > 0) {
				final Iterator<?> keyItr = map.keySet().iterator();
				while (keyItr.hasNext()) {
					final Object k = keyItr.next();
					final Object v = map.get(k);
					writer.writeObject(k);
					writer.writeObject(v);
				}
			}
		}
	};
	
	private final static Comparator<StreamableObjectType> COMPARATOR_OBJID = new Comparator<StreamableObjectType>() {
		@Override
		public int compare(final StreamableObjectType o1, final StreamableObjectType o2) {
			final int i1 = o1.objId, i2 = o2.objId;
			return i1 < i2 ? -1 : (i1 > i2 ? 1 : 0);
		}
	};
	
	private final static Comparator<StreamableObjectType> COMPARATOR_BITID = new Comparator<StreamableObjectType>() {
		@Override
		public int compare(final StreamableObjectType o1, final StreamableObjectType o2) {
			final int i1 = o1.bitId, i2 = o2.bitId;
			return i1 < i2 ? -1 : (i1 > i2 ? 1 : 0);
		}
	};

	final static StreamableObjectType[] OBJID_TABLE = instanceArray(COMPARATOR_OBJID);
	final static StreamableObjectType[] BITID_TABLE = instanceArray(COMPARATOR_BITID);
	
	@SuppressWarnings("serial")
	final static Lookup<Class<?>, StreamableObjectType> CLASS_TYPE_MAP; static {
		final Lookup<Class<?>, StreamableObjectType> l = new AtomicArrayLookup<>();
		for (final StreamableObjectType type : BITID_TABLE) {
			if (type.valueClass != null)
				l.put(type.valueClass, type);
		}
		CLASS_TYPE_MAP = l;
	}
	
	final Class<?> valueClass;
	final int objId;
	final int bitId;

	private StreamableObjectType(final Class<?> valueClass, final int objId) {
		this(valueClass, objId, objId);
	}

	private StreamableObjectType(final Class<?> valueClass, final int objId, final int bitId) {
		this.valueClass = valueClass;
		this.objId = objId;
		this.bitId = bitId;
		synchronized (INSTANCES) {
			if (!INSTANCES.contains(this))
				INSTANCES.add(this);
		}
	}
	
	private static StreamableObjectType[] instanceArray(final Comparator<StreamableObjectType> order) {
		synchronized (INSTANCES) {
			Collections.sort(INSTANCES, order);
			return Collection.toArray(INSTANCES, StreamableObjectType.class);
		}
	}

	// ----------------------------------------------------------
	// ---
	// --- IO template methods
	// ---
	// ----------------------------------------------------------
	abstract void write(final StreamWriterBase writer, final Object value) throws IOException;
	
	abstract Object read(final StreamReader reader) throws IOException;

	// ----------------------------------------------------------
	// ---
	// --- Object overrides
	// ---
	// ----------------------------------------------------------
	@Override
	public int hashCode() {
		return bitId;
	}

	@Override
	public boolean equals(final Object o) {
		return o instanceof StreamableObjectType && ((StreamableObjectType) o).bitId == bitId;
	}

	@Override
	public String toString() {
		return Strings.concat(Primitives.getSimpleName(getClass()), "[", bitId, "]");
	}
	
}
