package ch.eisenring.core.io;

import java.io.IOException;
import java.util.Date;

import ch.eisenring.core.datatypes.primitives.Primitives;
import ch.eisenring.core.datatypes.strings.StringMaker;
import ch.eisenring.core.datatypes.strings.Strings;
import ch.eisenring.core.datatypes.strings.cache.StringCache;
import ch.eisenring.core.tag.Tags;

final class StreamReaderDump extends StreamReaderBase {

	protected StreamReaderDump(final Tags tags) {
		super(tags);
	}

	@Override
	public boolean readBoolean() throws IOException {
		final int b = read8();
		if (b == 0)
			return false;
		if (b == 0xFF)
			return true;
		throw new StreamException("invalid boolean encoding: "+b);
	}
	
	@Override
	public char readChar() throws IOException {
		return (char) ((read8() << 8) | read8());
	}

	@Override
	public byte readByte() throws IOException {
		return (byte) read8();
	}

	@Override
	public short readShort() throws IOException {
		return (short) ((read8() << 8) | read8());
	}

	@Override
	public int readInt() throws IOException {
		return (read8() << 24) | (read8() << 16) | (read8() << 8) | read8();
	}

	@Override
	public long readLong() throws IOException {
		return (((long) readInt()) << 32) | (readInt() & 0xFFFFFFFFL);
	}
	
	@Override
	public float readFloat() throws IOException {
		return Float.intBitsToFloat(readInt());
	}
	
	@Override
	public double readDouble() throws IOException {
		return Double.longBitsToDouble(readLong());
	}

	@Override
	public byte[] readBinary() throws IOException {
		int l = readInt();
		if (l < 0) {
			if (l == -1)
				return null;
			throw new StreamException("invalid binary encoding: "+l);
		} else {
			final byte[] b;
			try {
				b = Primitives.newByteArray(l);
			} catch (final OutOfMemoryError e) {
				throw new StreamException(Strings.concat("failed to allocate byte[", l, "]: ", e.getMessage()));
			}
			int o = 0;
			while (l > 0) {
				int r = input.read(b, o, l);
				if (r == 0) {
					throw new StreamException("unexpected EOF @ "+position);
				} else if (r < 0) {
					throw new IOException(Strings.concat("read returned: ", r, " @ ", position));
				} else {
					o += r;
					l -= r;
				}
				position += r;
			}
			return b;
		}
	}

	private CharSequence readStringInternal() throws IOException {
		final int l = readInt();
		if (l <= 0) {
			switch (l) {
				case -1: return null;
				case 0: return "";
				default: throw new StreamException(Strings.concat("invalid string length encoding: ", l));
			}
		}
		final StringMaker builder = StringMaker.obtain(l);
		for (int i=0; i<l; ++i)
			builder.append(readChar());
		return builder.release();
	}

	@Override
	public String readString() throws IOException {
		final CharSequence charSeq = readStringInternal();
		return charSeq == null ? null : charSeq.toString();
	}

	@Override
	public String readString(final StringCache cache) throws IOException {
		final CharSequence charSeq = readStringInternal();
		return charSeq == null ? null : cache.get(charSeq);
	}
	
	@Override
	public Date readDate() throws IOException {
		final long l = readLong();
		if (l == Long.MIN_VALUE) {
			return null;
		} else {
			return new Date(l);
		}
	}

	@Override
	public Class<?> readClass() throws IOException {
		final String canonical = readString();
		if (canonical == null)
			return null;
		try {
			final Class<?> classRef = Primitives.classForName(canonical);
			return classRef;
		} catch (final ClassNotFoundException e) {
			throw new IOException(e.getMessage(), e);
		}
	}

}
