package ch.eisenring.core.io;

import java.io.IOException;
import java.io.OutputStream;
import java.io.Serializable;

import ch.eisenring.core.codetype.AbstractCode;
import ch.eisenring.core.codetype.DynamicCode;
import ch.eisenring.core.datatypes.strings.Strings;
import ch.eisenring.core.io.binary.BinaryHolder;
import ch.eisenring.core.tag.Tags;

/**
 * Helper class for serialization
 */
abstract class StreamWriterBase extends StreamableBase implements StreamWriter {

	/**
	 * 0 = dump class names
	 * 1 = replace class names with id.
	 */
	@Deprecated
	protected final int OPTIONS;

	protected final AssociationClassToId CLASS2ID = new AssociationClassToId();
	protected final OutputStream output;
	protected final boolean stringCacheEnumeration;

	protected StreamWriterBase(final Tags tags) {
		super(tags);
		OutputStream output = tags.get(StreamableTags.SINK);
		if (output == null)
			throw new NullPointerException("sink must not be NULL");
		final int bufferSize = StreamableTags.Util.getBufferSize(tags);
		if (bufferSize > 0)
			output = Streams.makeBuffered(output, bufferSize);
		this.OPTIONS = tags.get(StreamableTags.LEGACY_OPTIONS, 1);
		this.stringCacheEnumeration = tags.get(StreamableTags.STRINGENUMERATION, Boolean.FALSE);
		this.output = output;
	}

	@Override
	public void close() throws IOException {
		flush();
		output.close();
	}
	
	/**
	 * Flushes any buffered bytes and the underlying stream hierarchy.
	 */
	public void flush() throws IOException {
		output.flush();
	}
	
	/**
	 * Flushes data held in internal write buffers to the stream.
	 * 
	 * This will not force a flush of the underlying stream, it
	 * only ensure the internal buffers are completely written to
	 * the stream.
	 */
	public void flushBuffers() throws IOException {
		// the basic stream has no internal buffers
	}
	
	@Override
	public final void writeBinaryHolder(final BinaryHolder binary) throws IOException {
		if (binary == null) {
			writeLong(-1);
		} else {
			synchronized (binary.getLock()) {
				final long size = binary.size();
				writeLong(size);
				if (size > 0) {
					flushBuffers();
					binary.writeTo(output);
					position += size;
				}
			}
		}
	}

	@Override
	public void writeCode(final AbstractCode c) throws IOException {
		switch (codeStreamingMode) {
			default:
				throw new IOException("invalid code streaming mode: " + codeStreamingMode);
			case 0:
				// standard mode, by code id
				writeInt(c == null ? Integer.MIN_VALUE : c.getId());
				return;
			case 1:
				// persistent mode, by key if its a dynamic code
				final boolean isNull = c == null;
				writeBoolean(isNull);
				if (isNull)
					return;
				if (c instanceof DynamicCode) {
					writeObject(c.getKey());
				} else {
					writeInt(c.getId());
				}
				return;
		}
	}

	@Override
	public void writeClass(final Class<?> classRef) throws IOException {
		CLASS2ID.writeClass(this, classRef);
	}

	/**
	 * Writes an object to the stream. Note that this
	 * should only be used if the type is not statically
	 * known - the encoding will consume (on average) more bytes
	 * (in some cases MANY more) than the fixed type methods.
	 * 
	 * Only a few selected classes are supported:
	 * - NULL
	 * - all primitive wrappers
	 * - one-dimensional arrays of any primitive type
	 * - java.lang.String, java.util.Date
	 * - java.awt.Point, java.awt.Dimension, java.awt.Rectangle
	 * - any sub type of AbstractCode
	 * - any type that implements AutoStreamable (or Streamable)
	 * - any type implementing BinaryHolder
	 */
	@Override
	public final void writeObject(final Object o) throws IOException {
		if (o == null) {
			StreamableObjectType.NULL.write(this, o);
			return;
		}
		final Class<?> valueClass = o.getClass();
		StreamableObjectType objectId;
		objectId = StreamableObjectType.CLASS_TYPE_MAP.get(valueClass);
		if (objectId == null) {
			// must check for some sub types explicitly
			if (o instanceof AbstractCode) {
				objectId = StreamableObjectType.CODE;
			} else if (o instanceof AutoStreamable) {
				objectId = StreamableObjectType.STREAMABLE;
			} else if (o instanceof BinaryHolder) {
				objectId = StreamableObjectType.BINARY_HOLDER;
			} else if (valueClass.isArray()) {
				objectId = StreamableObjectType.OBJECT_ARRAY;
			} else if (o instanceof java.util.List) {
				objectId = StreamableObjectType.LIST;
			} else if (o instanceof java.util.Set) {
				objectId = StreamableObjectType.SET;
			} else if (o instanceof java.util.Map) {
				objectId = StreamableObjectType.MAP;
			} else if (o instanceof Serializable) {
				objectId = StreamableObjectType.SERIALIZABLE;
			} else {
				throw new IOException(Strings.concat("class ", valueClass, " not supported"));
			}
			// remember the type decision for later
			StreamableObjectType.CLASS_TYPE_MAP.put(valueClass, objectId);
		}
		objectId.write(this, o);
	}

	/**
	 * Writes an object id. 
	 */
	protected void writeObjectId(final StreamableObjectType objectId) throws IOException {
		writeByte((byte) objectId.objId);
	}

}
