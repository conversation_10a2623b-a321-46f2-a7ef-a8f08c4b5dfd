package ch.eisenring.core.threading;

final class ThreadParameters {

	private final int hashCode;
	private final long stackSize;
	private final ThreadGroup group;
	private final boolean daemon;

	public ThreadParameters(final ThreadParameters params, final long stackSize) {
		this(params.group, stackSize, params.daemon);
	}

	public ThreadParameters(final ThreadParameters params, final boolean daemon) {
		this(params.group, params.stackSize, daemon);
	}
	
	public ThreadParameters(final ThreadGroup group, final long stackSize, final boolean daemon) {
		this.hashCode = (group == null ? 0 : group.hashCode() * 71) ^ ((int) stackSize); 
		this.group = group;
		this.stackSize = stackSize;
		this.daemon = daemon;
	}

	// --------------------------------------------------------------
	// ---
	// --- Getters
	// ---
	// --------------------------------------------------------------
	public long getStackSize() {
		return stackSize;
	}

	public ThreadGroup getGroup() {
		return group;
	}

	public boolean isDaemon() {
		return daemon;
	}

	// --------------------------------------------------------------
	// ---
	// --- Object overrides
	// ---
	// --------------------------------------------------------------
	@Override
	public int hashCode() {
		return hashCode;
	}

	@Override
	public boolean equals(final Object o) {
		if (o == this)
			return true;
		if (o instanceof ThreadParameters) {
			final ThreadParameters p = (ThreadParameters) o;
			return p.stackSize == stackSize &&
				   p.daemon == daemon &&
				   p.group.equals(group);
		}
		return false;
	}

}
