package ch.eisenring.core.threading;

public interface ThreadKillListener {

	/**
	 * Called when a thread has been forcefully killed.
	 * Listeners may use this hook to clean up their references
	 * (e.g. remove the thread from a pool)
	 * 
	 * The boolean hardKilled is true if the thread had been killed
	 * in a forceful way, that means Thread.stop(). The listener
	 * may need to handle that case differently, since the thread
	 * most likely did not clean up itself.
	 */
	public void threadKilled(final Thread killedThread, final boolean hardKilled);
	
}
