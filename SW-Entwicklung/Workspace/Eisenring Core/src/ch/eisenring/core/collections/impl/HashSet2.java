package ch.eisenring.core.collections.impl;

import java.util.AbstractSet;
import java.util.Arrays;
import java.util.ConcurrentModificationException;
import java.util.Iterator;
import java.util.NoSuchElementException;

import ch.eisenring.core.collections.Set;
import ch.eisenring.core.collections.SizingPolicy;
import ch.eisenring.core.datatypes.primitives.Primitives;
import ch.eisenring.core.datatypes.strings.StringMaker;
import ch.eisenring.core.datatypes.strings.StringMakerFriendly;
import ch.eisenring.core.iterators.EmptyIterator;

/**
 * HashSet implementation not based on delegating to HashMap.
 * A bit less memory hungry than the JRE HashSet (and often a bit faster)
 */
public class HashSet2<E> extends AbstractSet<E> implements Set<E>, StringMakerFriendly {

	/**
	 * Internal entry structure
	 *
	 * The structure is tailored for 8 bytes object overhead
	 * plus 4 byte reference cost (for a total of 16 bytes per entry)
	 */
	final static class HashSetEntry {
		HashSetEntry next;
		Object element;
		
		public HashSetEntry(final Object element) {
			this.element = element;
		}
	}

	final static float MIN_LOADFACTOR = 1F/16;
	final static float MAX_LOADFACTOR = 16;
	final static float DEFAULT_LOADFACTOR = 3F/4;

	final static int MIN_BUCKET_COUNT = 4;
	final static int MAX_BUCKET_COUNT = 1 << 30;

	final static Iterator<?> EMPTY_ITERATOR = EmptyIterator.INSTANCE;
	final static HashSetEntry[] EMPTY_ENTRY_ARRAY = new HashSetEntry[0];
	final static int NULL_HASH = 0;

	HashSetEntry[] buckets;
	int size;
	int modCount;
	int resizeTrigger; 
	float loadFactor;

	// --------------------------------------------------------------
	// ---
	// --- Constructors
	// ---
	// --------------------------------------------------------------
	/**
	 * Creates a set with default capacity and load factor
	 */
	public HashSet2() {
		this.buckets = EMPTY_ENTRY_ARRAY;
		this.resizeTrigger = -1;
		this.loadFactor = DEFAULT_LOADFACTOR;
	}

	/**
	 * Creates a set with given initial capacity.
	 */
	public HashSet2(final int initialCapacity) {
		this.loadFactor = DEFAULT_LOADFACTOR;
		init(initialCapacity);
	}
	
	/**
	 * Creates a set with given initial capacity.
	 */
	public HashSet2(final int initialCapacity, final float loadFactor) {
        this.loadFactor = validateLoadFactor(loadFactor);
        init(initialCapacity);
	}

	private void init(final int initialCapacity) {
        if (initialCapacity <= MIN_BUCKET_COUNT) {
			this.buckets = EMPTY_ENTRY_ARRAY;
			this.resizeTrigger = -1;
		} else if (initialCapacity >= MAX_BUCKET_COUNT) {
			this.buckets = new HashSetEntry[MAX_BUCKET_COUNT];
			this.resizeTrigger = Integer.MAX_VALUE;
		} else {
			this.buckets = new HashSetEntry[bucketCountFor(initialCapacity)];
			this.resizeTrigger = getLoadLimit(buckets.length, loadFactor); 
		}
	}

	private static float validateLoadFactor(final float loadFactor) {
        if (loadFactor <= 0 || Float.isNaN(loadFactor))
            throw new IllegalArgumentException("Illegal load factor: " + loadFactor);
        if (loadFactor < MIN_LOADFACTOR)
        	return MIN_LOADFACTOR;
        if (loadFactor > MAX_LOADFACTOR)
        	return MAX_LOADFACTOR;
        return loadFactor;
	}

    /**
     * Returns a power of two size for the given target capacity.
     */
    private static int bucketCountFor(final int capacity) {
        int n = capacity - 1;
        n |= n >>> 1;
        n |= n >>> 2;
        n |= n >>> 4;
        n |= n >>> 8;
        n |= n >>> 16;
        return (n < 0) ? 1 : (n >= MAX_BUCKET_COUNT) ? MAX_BUCKET_COUNT : n + 1;
    }

    /**
     * Calculates size * factor, flushing to Integer.MAX_VALUE on overflow
     */
    private static int getLoadLimit(final int size, final float factor) {
    	return (int) ((size * factor) + 1F);
    }
    
    private int bucketIndexForHash(final int hash) {
		final int i = ((hash * 71) >> 16) ^ hash;
    	return (buckets.length - 1) & i;
	}

    private void ensureBucketCapacity() {
    	if (size <= resizeTrigger)
   			return;
    	int newBucketCount = buckets.length;
    	if (newBucketCount < MIN_BUCKET_COUNT) {
    		newBucketCount = MIN_BUCKET_COUNT;
    	} else if (newBucketCount < MAX_BUCKET_COUNT) {
    		newBucketCount <<= 1;
    	}
		resizeTrigger = getLoadLimit(newBucketCount, loadFactor);
    	resize(newBucketCount);
    }

	private void resize(final int newBucketCount) {
		final HashSetEntry[] oldBuckets = buckets;
		if (oldBuckets.length == newBucketCount)
			return;
		final HashSetEntry[] newBuckets = new HashSetEntry[newBucketCount];
		buckets = newBuckets;
		if (size > 0){
			for (HashSetEntry entry : oldBuckets) {
				while (entry != null) {
					final HashSetEntry next = entry.next;
					final int index = bucketIndexForHash(hashCode(entry.element));
					entry.next = newBuckets[index];
					newBuckets[index] = entry;
					entry = next;
				}
			}
		}
	}

	// --------------------------------------------------------------
	// ---
	// --- Set implementation
	// ---
	// --------------------------------------------------------------
	@Override
	public boolean isEmpty() {
		return size == 0;
	}

	@Override
	public int size() {
		return size;
	}

	@Override
	public boolean contains(final Object object) {
		if (size == 0)
			return false;
		HashSetEntry entry;
		if (object == null) {
			// return fast if bucket is empty
			if ((entry = buckets[bucketIndexForHash(NULL_HASH)]) == null)
				return false;
			// check out the bucket chain
			do {
				if (entry.element == null)
					return true;
			} while ((entry = entry.next) != null);
			return false;
		} else {
			// search for non-NULL element
			if ((entry = buckets[bucketIndexForHash(object.hashCode())]) == null)
				return false;
			do {
				if (object.equals(entry.element))
					return true;
			} while ((entry = entry.next) != null);
			return false;
		}
	}

	@Override
	public void clear() {
		if (size != 0) {
			++modCount;
			Arrays.fill(buckets, (HashSetEntry) null);
			size = 0;
		}
	}

	@Override
	public boolean add(final E element) {
		ensureBucketCapacity();
		final int index;
		HashSetEntry e;
		if (element == null) {
			// add NULL element
			index = bucketIndexForHash(NULL_HASH);
			if ((e = buckets[index]) != null) {
				do {
					if (e.element == null)
						return false;
				} while ((e = e.next) != null);
			}
		} else {
			// add non-NULL element
			index = bucketIndexForHash(element.hashCode());
			if ((e = buckets[index]) != null) {
				do {
					if (element.equals(e.element))
						return false;
				} while ((e = e.next) != null);
			}
		}
		e = new HashSetEntry(element);
		++modCount;
		++size;
		e.next = buckets[index];
		buckets[index] = e;
		return true;
	}

	@Override
	public boolean remove(final Object element) {
		if (size == 0)
			return false;
		final int index;
		HashSetEntry e, p;
		if (element == null) {
			// remove NULL element
			index = bucketIndexForHash(NULL_HASH);
			p = buckets[index];
			if (p == null)
				return false;
			if (p.element == null) {
				++modCount;
				--size;
				buckets[index] = p.next;
				return true;
			}
			while ((e = p.next) != null) {
				if (e.element == null) {
					++modCount;
					--size;
					p.next = e.next;
					return true;
				}
				p = e;
			}
			return false;
		} else {
			// remove non-NULL element
			index = bucketIndexForHash(element.hashCode());
			p = buckets[index];
			if (p == null)
				return false;
			if (element.equals(p.element)) {
				++modCount;
				--size;
				buckets[index] = p.next;
				return true;
			}
			while ((e = p.next) != null) {
				if (element.equals(e.element)) {
					++modCount;
					--size;
					p.next = e.next;
					return true;
				}
				p = e;
			}
			return false;
		}
	}

	// --------------------------------------------------------------
	// ---
	// --- Iterator
	// ---
	// --------------------------------------------------------------
	class HashSetIterator implements Iterator<E> {
		// initial mod count
		int expectedModCount;

		int index, remi;
		HashSetEntry curr, remv;
		
		public HashSetIterator() {
			expectedModCount = modCount;
			while (index < buckets.length && (curr = buckets[index]) == null) {
				++index;
			}
		}

		@Override
		public boolean hasNext() {
            if (modCount != expectedModCount)
                throw new ConcurrentModificationException();
            return curr != null;
        }

		@SuppressWarnings("unchecked")
		@Override
		public E next() {
            if (modCount != expectedModCount)
                throw new ConcurrentModificationException();
            if (curr == null)
            	throw new NoSuchElementException();
            remv = curr;
            remi = index;
            curr = curr.next;
            if (curr == null) {
            	// go to next linked chain
            	while (++index < buckets.length && (curr = buckets[index]) == null);
            }
            return (E) remv.element;
		}

		@Override
		public void remove() {
            if (modCount != expectedModCount)
                throw new ConcurrentModificationException();
            if (remv == null)
            	throw new IllegalStateException();
            HashSetEntry pred = buckets[remi];
            if (pred == remv) {
            	buckets[remi] = remv.next;
            } else {
            	while (pred.next != remv) {
            		pred = pred.next;
            	}
            	pred.next = remv.next;
            }
            expectedModCount = ++modCount;
            remv = null;
            --size;
		}
	}

	@SuppressWarnings("unchecked")
	@Override
	public Iterator<E> iterator() {
		return size == 0 ? (Iterator<E>) EMPTY_ITERATOR : new HashSetIterator();
	}
	
	@Override
	public int hashCode() {
		if (size == 0)
			return 0;
		int hash = 0;
		for (HashSetEntry e : buckets) {
			while (e != null) {
				final Object o;
				if ((o = e.element) != null)
					hash += o.hashCode();
				e = e.next;
			}
		}
		return hash;
	}

	private static int hashCode(final Object object) {
		return object == null ? NULL_HASH : object.hashCode();
	}

	// --------------------------------------------------------------
	// ---
	// --- toArray
	// ---
	// --------------------------------------------------------------
	@Override
	public Object[] toArray() {
		if (size == 0)
			return Primitives.EMPTY_OBJECT_ARRAY;
		int i = size;
		final Object[] array = new Object[i];
		for (HashSetEntry e : buckets) {
			while (e != null) {
				array[--i] = e.element;
				e = e.next;
			}
		}
		return array;
	}

	@SuppressWarnings("unchecked")
	@Override
	public <T> T[] toArray(final T[] a) {
        int i = size;
        final T[] array;
        if (a.length >= i) {
        	array = a;
        	if (array.length > i)
        		array[i] = null;
        } else {
        	array = (T[]) java.lang.reflect.Array.newInstance(a.getClass().getComponentType(), i);
        }
        if (i == 0)
        	return array;
        for (HashSetEntry e : buckets) {
        	while (e != null) {
        		array[--i] = (T) e.element;
        		e = e.next;
        	}
        }
        return array;
	}

	// --------------------------------------------------------------
	// ---
	// --- Additional size control API
	// ---
	// --------------------------------------------------------------
	@Override
	public void ensureCapacity(final int capacity) {
		final int minCapacity = getLoadLimit(capacity, loadFactor);
		if (minCapacity <= buckets.length) {
			return;
		} else if (minCapacity < MIN_BUCKET_COUNT) {
			resize(MIN_BUCKET_COUNT);
		} else {
			resize(bucketCountFor(minCapacity));
		}
	}

	@Override
	public void setPolicy(final SizingPolicy policy) {
		// NO-OP
	}

	@Override
	public void trimToSize() {
		if (size == 0) {
			buckets = EMPTY_ENTRY_ARRAY;
			resizeTrigger = 0;
		}
	}

	// --------------------------------------------------------------
	// ---
	// --- Object overrides
	// ---
	// --------------------------------------------------------------
	@Override
	public String toString() {
		if (size == 0)
			return "[]";
		return StringMakerFriendly.toString(this);
	}

	@Override
	public void toStringMaker(final StringMaker target) {
		String s = "";
		target.append('[');
		for (HashSetEntry e : buckets) {
			while (e != null) {
				target.append(s);
				s = ", ";
				final Object o = e.element;
				target.append(o == this ? "(this Collection)" : o);
				e = e.next;
			}
		}
		target.append(']');
	}

//	private String getStructureDescription(final String method) {
//		final StringMaker b = StringMaker.obtain();
//		b.append(method);
//		b.append(" : HashSet2[");
//		for (int i=0; i<buckets.length; ++i) {
//			b.append("\n [");
//			b.append(i);
//			b.append("] (");
//			Entry e = buckets[i];
//			if (e != null) {
//				b.append(e.element);
//				e = e.next;
//				while (e != null) {
//					b.append(" -> ");
//					b.append(e.element);
//					e = e.next;
//				}
//			}
//			b.append(")");
//		}
//		b.append("\n]");
//		return b.release();
//	}

}
