package ch.eisenring.core.application;

import java.io.IOException;
import java.io.InputStream;
import java.nio.charset.Charset;

import ch.eisenring.core.datatypes.date.TimestampUtil;
import ch.eisenring.core.datatypes.strings.StringMaker;
import ch.eisenring.core.datatypes.strings.Strings;
import ch.eisenring.core.io.Streams;
import ch.eisenring.core.io.file.FileItem;
import ch.eisenring.core.io.file.FileItemProperties;
import ch.eisenring.core.logging.Logger;

abstract class ManagedFile {

	private final String filename;

	private boolean exists;
	private long checkTimestamp = Long.MIN_VALUE;
	private long lastModified = TimestampUtil.NULL_TIMESTAMP;
	private long length = -1;

	protected ManagedFile(final CharSequence filename) {
		this.filename = Strings.toString(filename);
	}

	/**
	 * Gets the minimum time between change checks (in milliseconds)
	 */
	protected abstract long getMinChangeCheckInterval();

	/**
	 * Gets the File managed by this instance
	 */
	abstract protected FileItem getFileItem();

	public final String getFilename() {
		return filename;
	}

	/**
	 * Gets an input stream of the current file data
	 */
	public InputStream getInputStream() throws IOException {
		final InputStream fileIn;
		try {
			 fileIn = getFileItem().getInputStream();
		} catch (final IOException e) {
			Logger.error("File not found: " + getFileItem().getAbsolutePath());
			throw e;
		}
		try {
			return Streams.makeBuffered(fileIn, Streams.DEFAULT_BUFFER_SIZE);
		} catch (final RuntimeException e) {
			Streams.closeSilent(fileIn);
			throw e;
		}
	}

	/**
	 * Gets the current file data as String, using the provided CharSet
	 * for conversion.
	 */
	public String getTextContent(final Charset encoding) throws IOException {
		try (InputStream input = getInputStream()) {
			final StringMaker buffer = StringMaker.obtain(1024);
			buffer.appendFrom(input, encoding);
			return buffer.release();
		}
	}

	// --------------------------------------------------------------
	// ---
	// --- Change detection
	// ---
	// --------------------------------------------------------------
	/**
	 * Checks if this file has been changed.
	 * This will return true if the file has been changed or
	 * has been newly created. It will return false if the file
	 * does not exist.
	 */
	public final boolean isChanged() {
		final long now = System.currentTimeMillis();
		boolean result = false;
		Logger.info("Checking if file has changed: '{}'", filename);
		try {
			do {
				if (checkTimestamp > now) {
					Logger.info("Skipping check, too soon since last check");
					break;
				} else {
					checkTimestamp = getMinChangeCheckInterval() + now;
					Logger.info("Performing check, min interval: {} ms", getMinChangeCheckInterval());
				}
				final FileItem fileItem = getFileItem();
				Logger.info("FileItem path: '{}'", fileItem.getAbsolutePath());

				final FileItemProperties properties = fileItem.getProperties();
				Logger.info("File properties - exists: {}, isFile: {}", properties.exists(), properties.isFile());

				if (!properties.exists() || !properties.isFile()) {
					boolean wasExisting = this.exists;
					this.exists = false; // Update the exists flag
					Logger.info("File does not exist or is not a file, was existing: {}", wasExisting);
					result = false;
					break;
				}

				final long lastModified = properties.lastModified();
				final long length = properties.length();
				Logger.info("File stats - lastModified: {}, length: {}", lastModified, length);
				Logger.info("Previous stats - exists: {}, lastModified: {}, length: {}",
					this.exists, this.lastModified, this.length);

				if (!this.exists ||
					 this.lastModified != lastModified ||
					 this.length != length) {
					Logger.info("File has changed, updating stats");
					this.exists = true;
					this.lastModified = lastModified;
					this.length = length;
					result = true;
				} else {
					Logger.info("File has not changed");
				}
			} while (false);
		} catch (final Exception e) {
			Logger.error("Error checking if file has changed: {}", e.getMessage());
			Logger.error(e);
			result = false;
		}
		Logger.info("isChanged result: {}", result);
		return result;
	}

	/**
	 * Returns true if the file exists (or rather existed
	 * the last time isChanged() was called). It may still be inaccessible
	 * or the contents may be invalid.
	 */
	public final boolean exists() {
		// Add logging to see when this method is called and what it returns
		Logger.info("Checking if file exists: '{}', cached result: {}", filename, exists);

		// Also check the current state directly
		FileItem fileItem = getFileItem();
		FileItemProperties props = fileItem.getProperties();
		boolean currentExists = props.exists() && props.isFile();

		Logger.info("Current file state - exists: {}, isFile: {}", props.exists(), props.isFile());

		// If there's a discrepancy, log it
		if (exists != currentExists) {
			Logger.warn("File existence state mismatch - cached: {}, current: {}", exists, currentExists);
		}

		return exists;
	}

	// --------------------------------------------------------------
	// ---
	// --- Object overrides
	// ---
	// --------------------------------------------------------------
	@Override
	public String toString() {
		return filename;
	}

}
