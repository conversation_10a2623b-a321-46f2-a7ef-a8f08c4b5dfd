package ch.eisenring.core.platform.windows;

import java.io.IOException;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import ch.eisenring.core.logging.Logger;

/**
 * Windows Registry access using JNA.
 * This implementation is compatible with Java 21 and doesn't rely on internal JDK APIs.
 */
public final class JnaWinRegistry {

    // Windows registry root keys
    public static final int HKEY_CURRENT_USER = 0x80000001;
    public static final int HKEY_LOCAL_MACHINE = 0x80000002;

    // Default value name
    public static final String STANDARD = "";

    // Registry access rights
    private static final int KEY_READ = 0x20019;
    private static final int KEY_WRITE = 0x20006;
    private static final int KEY_ALL_ACCESS = 0xF003F;

    // Error codes
    private static final int ERROR_SUCCESS = 0;
    private static final int ERROR_FILE_NOT_FOUND = 2;
    private static final int ERROR_ACCESS_DENIED = 5;

    // JNA interfaces for Windows Registry API - using explicit "A" (ANSI) or "W" (Unicode) suffixes
    private interface Advapi32 extends com.sun.jna.win32.StdCallLibrary {
        Advapi32 INSTANCE = com.sun.jna.Native.load("advapi32", Advapi32.class);

        int RegOpenKeyExA(int hKey, String lpSubKey, int ulOptions, int samDesired, com.sun.jna.ptr.IntByReference phkResult);
        int RegCloseKey(int hKey);
        int RegQueryValueExA(int hKey, String lpValueName, int lpReserved, com.sun.jna.ptr.IntByReference lpType,
                           byte[] lpData, com.sun.jna.ptr.IntByReference lpcbData);
        int RegEnumValueA(int hKey, int dwIndex, byte[] lpValueName, com.sun.jna.ptr.IntByReference lpcchValueName,
                        int lpReserved, com.sun.jna.ptr.IntByReference lpType, byte[] lpData, com.sun.jna.ptr.IntByReference lpcbData);
        int RegEnumKeyExA(int hKey, int dwIndex, byte[] lpName, com.sun.jna.ptr.IntByReference lpcchName,
                        com.sun.jna.ptr.IntByReference lpReserved, byte[] lpClass, com.sun.jna.ptr.IntByReference lpcchClass,
                        com.sun.jna.ptr.LongByReference lpftLastWriteTime);
        int RegCreateKeyExA(int hKey, String lpSubKey, int Reserved, String lpClass, int dwOptions,
                          int samDesired, com.sun.jna.Pointer lpSecurityAttributes, com.sun.jna.ptr.IntByReference phkResult,
                          com.sun.jna.ptr.IntByReference lpdwDisposition);
        int RegSetValueExA(int hKey, String lpValueName, int Reserved, int dwType, byte[] lpData, int cbData);
        int RegDeleteKeyA(int hKey, String lpSubKey);
        int RegDeleteValueA(int hKey, String lpValueName);
        int RegQueryInfoKeyA(int hKey, byte[] lpClass, com.sun.jna.ptr.IntByReference lpcchClass,
                           com.sun.jna.ptr.IntByReference lpReserved, com.sun.jna.ptr.IntByReference lpcSubKeys,
                           com.sun.jna.ptr.IntByReference lpcbMaxSubKeyLen, com.sun.jna.ptr.IntByReference lpcbMaxClassLen,
                           com.sun.jna.ptr.IntByReference lpcValues, com.sun.jna.ptr.IntByReference lpcbMaxValueNameLen,
                           com.sun.jna.ptr.IntByReference lpcbMaxValueLen, com.sun.jna.ptr.IntByReference lpcbSecurityDescriptor,
                           com.sun.jna.ptr.LongByReference lpftLastWriteTime);
    }

    // Flag to check if JNA is available
    private static final boolean JNA_AVAILABLE;

    static {
        boolean jnaAvailable = false;
        try {
            // Try to load JNA classes
            Class.forName("com.sun.jna.Native");
            Class.forName("com.sun.jna.win32.StdCallLibrary");

            // Test if we can access the registry API
            try {
                com.sun.jna.ptr.IntByReference phkResult = new com.sun.jna.ptr.IntByReference();
                int result = Advapi32.INSTANCE.RegOpenKeyExA(HKEY_LOCAL_MACHINE,
                    "SOFTWARE\\Microsoft\\Windows NT\\CurrentVersion", 0, KEY_READ, phkResult);
                if (result == ERROR_SUCCESS) {
                    Advapi32.INSTANCE.RegCloseKey(phkResult.getValue());
                    jnaAvailable = true;
                }
            } catch (Throwable t) {
                Logger.warn("JNA registry access test failed: " + t.getMessage());
            }
        } catch (ClassNotFoundException e) {
            Logger.warn("JNA libraries not found. Windows registry access will be limited.");
        } catch (Throwable t) {
            Logger.warn("Error initializing JNA: " + t.getMessage());
        }
        JNA_AVAILABLE = jnaAvailable;
    }

    private JnaWinRegistry() {
        // Private constructor to prevent instantiation
    }

    /**
     * Checks if JNA is available for registry access.
     *
     * @return true if JNA is available, false otherwise
     */
    public static boolean isAvailable() {
        return JNA_AVAILABLE;
    }

    /**
     * Reads a string value from the registry.
     *
     * @param hkey HKEY_CURRENT_USER or HKEY_LOCAL_MACHINE
     * @param key Registry key path
     * @param valueName Value name to read (use STANDARD for default value)
     * @return The string value, or null if not found
     * @throws IOException If an error occurs
     */
    public static String readString(int hkey, String key, String valueName) throws IOException {
        if (!JNA_AVAILABLE) {
            throw new IOException("JNA is not available");
        }

        com.sun.jna.ptr.IntByReference phkResult = new com.sun.jna.ptr.IntByReference();
        int rc = Advapi32.INSTANCE.RegOpenKeyExA(hkey, key, 0, KEY_READ, phkResult);
        if (rc != ERROR_SUCCESS) {
            if (rc == ERROR_FILE_NOT_FOUND) {
                return null;
            }
            throw new IOException("RegOpenKeyEx error: " + rc);
        }

        try {
            com.sun.jna.ptr.IntByReference lpcbData = new com.sun.jna.ptr.IntByReference();
            com.sun.jna.ptr.IntByReference lpType = new com.sun.jna.ptr.IntByReference();

            // Get the required buffer size
            rc = Advapi32.INSTANCE.RegQueryValueExA(phkResult.getValue(), valueName, 0, lpType, null, lpcbData);
            if (rc != ERROR_SUCCESS) {
                if (rc == ERROR_FILE_NOT_FOUND) {
                    return null;
                }
                throw new IOException("RegQueryValueExA error: " + rc);
            }

            // Read the value
            byte[] buffer = new byte[lpcbData.getValue()];
            rc = Advapi32.INSTANCE.RegQueryValueExA(phkResult.getValue(), valueName, 0, lpType, buffer, lpcbData);
            if (rc != ERROR_SUCCESS) {
                throw new IOException("RegQueryValueExA error: " + rc);
            }

            // Convert to string (remove trailing null character)
            return new String(buffer, 0, buffer.length > 0 && buffer[buffer.length - 1] == 0 ? buffer.length - 1 : buffer.length);
        } finally {
            Advapi32.INSTANCE.RegCloseKey(phkResult.getValue());
        }
    }

    /**
     * Reads all values from a registry key.
     *
     * @param hkey HKEY_CURRENT_USER or HKEY_LOCAL_MACHINE
     * @param key Registry key path
     * @return Map of value names to values, or null if key not found
     * @throws IOException If an error occurs
     */
    public static Map<String, String> readStringValues(int hkey, String key) throws IOException {
        if (!JNA_AVAILABLE) {
            throw new IOException("JNA is not available");
        }

        com.sun.jna.ptr.IntByReference phkResult = new com.sun.jna.ptr.IntByReference();
        int rc = Advapi32.INSTANCE.RegOpenKeyExA(hkey, key, 0, KEY_READ, phkResult);
        if (rc != ERROR_SUCCESS) {
            if (rc == ERROR_FILE_NOT_FOUND) {
                return null;
            }
            throw new IOException("RegOpenKeyExA error: " + rc);
        }

        try {
            Map<String, String> results = new HashMap<>();

            // Get key info
            com.sun.jna.ptr.IntByReference lpcValues = new com.sun.jna.ptr.IntByReference();
            com.sun.jna.ptr.IntByReference lpcbMaxValueNameLen = new com.sun.jna.ptr.IntByReference();
            com.sun.jna.ptr.IntByReference lpcbMaxValueLen = new com.sun.jna.ptr.IntByReference();

            rc = Advapi32.INSTANCE.RegQueryInfoKeyA(
                phkResult.getValue(), null, null, null, null, null, null,
                lpcValues, lpcbMaxValueNameLen, lpcbMaxValueLen, null, null
            );

            if (rc != ERROR_SUCCESS) {
                throw new IOException("RegQueryInfoKeyA error: " + rc);
            }

            int valueCount = lpcValues.getValue();
            int maxValueNameLen = lpcbMaxValueNameLen.getValue() + 1; // Add 1 for null terminator
            int maxValueLen = lpcbMaxValueLen.getValue();

            // Enumerate values
            for (int i = 0; i < valueCount; i++) {
                byte[] valueName = new byte[maxValueNameLen];
                com.sun.jna.ptr.IntByReference lpcchValueName = new com.sun.jna.ptr.IntByReference(maxValueNameLen);
                byte[] valueData = new byte[maxValueLen];
                com.sun.jna.ptr.IntByReference lpcbData = new com.sun.jna.ptr.IntByReference(maxValueLen);
                com.sun.jna.ptr.IntByReference lpType = new com.sun.jna.ptr.IntByReference();

                rc = Advapi32.INSTANCE.RegEnumValueA(
                    phkResult.getValue(), i, valueName, lpcchValueName, 0,
                    lpType, valueData, lpcbData
                );

                if (rc != ERROR_SUCCESS) {
                    throw new IOException("RegEnumValueA error: " + rc);
                }

                String name = new String(valueName, 0, lpcchValueName.getValue());
                String value = new String(valueData, 0, lpcbData.getValue());

                // Remove trailing null character if present
                if (value.length() > 0 && value.charAt(value.length() - 1) == 0) {
                    value = value.substring(0, value.length() - 1);
                }

                results.put(name, value);
            }

            return results;
        } finally {
            Advapi32.INSTANCE.RegCloseKey(phkResult.getValue());
        }
    }

    /**
     * Reads all subkeys of a registry key.
     *
     * @param hkey HKEY_CURRENT_USER or HKEY_LOCAL_MACHINE
     * @param key Registry key path
     * @return List of subkey names, or null if key not found
     * @throws IOException If an error occurs
     */
    public static List<String> readStringSubKeys(int hkey, String key) throws IOException {
        if (!JNA_AVAILABLE) {
            throw new IOException("JNA is not available");
        }

        com.sun.jna.ptr.IntByReference phkResult = new com.sun.jna.ptr.IntByReference();
        int rc = Advapi32.INSTANCE.RegOpenKeyExA(hkey, key, 0, KEY_READ, phkResult);
        if (rc != ERROR_SUCCESS) {
            if (rc == ERROR_FILE_NOT_FOUND) {
                return null;
            }
            throw new IOException("RegOpenKeyExA error: " + rc);
        }

        try {
            List<String> results = new ArrayList<>();

            // Get key info
            com.sun.jna.ptr.IntByReference lpcSubKeys = new com.sun.jna.ptr.IntByReference();
            com.sun.jna.ptr.IntByReference lpcbMaxSubKeyLen = new com.sun.jna.ptr.IntByReference();

            rc = Advapi32.INSTANCE.RegQueryInfoKeyA(
                phkResult.getValue(), null, null, null,
                lpcSubKeys, lpcbMaxSubKeyLen, null, null, null, null, null, null
            );

            if (rc != ERROR_SUCCESS) {
                throw new IOException("RegQueryInfoKeyA error: " + rc);
            }

            int subKeyCount = lpcSubKeys.getValue();
            int maxSubKeyLen = lpcbMaxSubKeyLen.getValue() + 1; // Add 1 for null terminator

            // Enumerate subkeys
            for (int i = 0; i < subKeyCount; i++) {
                byte[] subKeyName = new byte[maxSubKeyLen];
                com.sun.jna.ptr.IntByReference lpcchName = new com.sun.jna.ptr.IntByReference(maxSubKeyLen);

                rc = Advapi32.INSTANCE.RegEnumKeyExA(
                    phkResult.getValue(), i, subKeyName, lpcchName,
                    null, null, null, null
                );

                if (rc != ERROR_SUCCESS) {
                    throw new IOException("RegEnumKeyExA error: " + rc);
                }

                String name = new String(subKeyName, 0, lpcchName.getValue());
                results.add(name);
            }

            return results;
        } finally {
            Advapi32.INSTANCE.RegCloseKey(phkResult.getValue());
        }
    }

    /**
     * Creates a registry key.
     *
     * @param hkey HKEY_CURRENT_USER or HKEY_LOCAL_MACHINE
     * @param key Registry key path
     * @throws IOException If an error occurs
     */
    public static void createKey(int hkey, String key) throws IOException {
        if (!JNA_AVAILABLE) {
            throw new IOException("JNA is not available");
        }

        com.sun.jna.ptr.IntByReference phkResult = new com.sun.jna.ptr.IntByReference();
        com.sun.jna.ptr.IntByReference lpdwDisposition = new com.sun.jna.ptr.IntByReference();

        int rc = Advapi32.INSTANCE.RegCreateKeyExA(
            hkey, key, 0, null, 0, KEY_ALL_ACCESS,
            null, phkResult, lpdwDisposition
        );

        if (rc != ERROR_SUCCESS) {
            if (rc == ERROR_ACCESS_DENIED) {
                throw new IOException("Access denied creating key: " + key);
            } else {
                throw new IOException("RegCreateKeyExA error: " + rc);
            }
        }

        Advapi32.INSTANCE.RegCloseKey(phkResult.getValue());
    }

    /**
     * Writes a string value to the registry.
     *
     * @param hkey HKEY_CURRENT_USER or HKEY_LOCAL_MACHINE
     * @param key Registry key path
     * @param valueName Value name to write (use STANDARD for default value)
     * @param value String value to write
     * @throws IOException If an error occurs
     */
    public static void writeStringValue(int hkey, String key, String valueName, String value) throws IOException {
        if (!JNA_AVAILABLE) {
            throw new IOException("JNA is not available");
        }

        com.sun.jna.ptr.IntByReference phkResult = new com.sun.jna.ptr.IntByReference();
        int rc = Advapi32.INSTANCE.RegOpenKeyExA(hkey, key, 0, KEY_WRITE, phkResult);
        if (rc != ERROR_SUCCESS) {
            throw new IOException("RegOpenKeyExA error: " + rc);
        }

        try {
            // Convert string to bytes (add null terminator)
            byte[] data = (value + "\0").getBytes();

            // REG_SZ = 1
            rc = Advapi32.INSTANCE.RegSetValueExA(phkResult.getValue(), valueName, 0, 1, data, data.length);
            if (rc != ERROR_SUCCESS) {
                throw new IOException("RegSetValueExA error: " + rc);
            }
        } finally {
            Advapi32.INSTANCE.RegCloseKey(phkResult.getValue());
        }
    }

    /**
     * Deletes a registry key.
     *
     * @param hkey HKEY_CURRENT_USER or HKEY_LOCAL_MACHINE
     * @param key Registry key path
     * @throws IOException If an error occurs
     */
    public static void deleteKey(int hkey, String key) throws IOException {
        if (!JNA_AVAILABLE) {
            throw new IOException("JNA is not available");
        }

        int rc = Advapi32.INSTANCE.RegDeleteKeyA(hkey, key);
        if (rc != ERROR_SUCCESS) {
            if (rc == ERROR_FILE_NOT_FOUND) {
                throw new IOException("Key not found: " + key);
            } else if (rc == ERROR_ACCESS_DENIED) {
                throw new IOException("Access denied deleting key: " + key);
            } else {
                throw new IOException("RegDeleteKeyA error: " + rc);
            }
        }
    }

    /**
     * Deletes a registry value.
     *
     * @param hkey HKEY_CURRENT_USER or HKEY_LOCAL_MACHINE
     * @param key Registry key path
     * @param valueName Value name to delete
     * @throws IOException If an error occurs
     */
    public static void deleteValue(int hkey, String key, String valueName) throws IOException {
        if (!JNA_AVAILABLE) {
            throw new IOException("JNA is not available");
        }

        com.sun.jna.ptr.IntByReference phkResult = new com.sun.jna.ptr.IntByReference();
        int rc = Advapi32.INSTANCE.RegOpenKeyExA(hkey, key, 0, KEY_WRITE, phkResult);
        if (rc != ERROR_SUCCESS) {
            if (rc == ERROR_FILE_NOT_FOUND) {
                throw new IOException("Key not found: " + key);
            }
            throw new IOException("RegOpenKeyExA error: " + rc);
        }

        try {
            rc = Advapi32.INSTANCE.RegDeleteValueA(phkResult.getValue(), valueName);
            if (rc != ERROR_SUCCESS) {
                if (rc == ERROR_FILE_NOT_FOUND) {
                    throw new IOException("Value not found: " + valueName);
                } else {
                    throw new IOException("RegDeleteValueA error: " + rc);
                }
            }
        } finally {
            Advapi32.INSTANCE.RegCloseKey(phkResult.getValue());
        }
    }
}
