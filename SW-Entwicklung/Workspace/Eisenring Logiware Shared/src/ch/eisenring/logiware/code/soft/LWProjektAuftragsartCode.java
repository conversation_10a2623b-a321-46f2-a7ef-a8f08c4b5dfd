package ch.eisenring.logiware.code.soft;

import ch.eisenring.core.codetype.LoadQuery;
import ch.eisenring.logiware.code.LogiwareAppCode;

@LoadQuery("SELECT " +
		     "C.CD_Hard AS ${Key}, " +
		     "C.CD_Soft AS ${ShortText}, " +
		     "C.Bezeichnung AS ${LongText} " +
		   "FROM " +
		     "${DB}${Schema}APPCODE AS C " +
		   "WHERE " +
		     "C.CD_Sprache = '001' AND " +
		     "C.Tab_Prfx = 'LPAU' AND " +
		     "C.Col_Name = 'PAUFTRART'")
public final class LWProjektAuftragsartCode extends LogiwareAppCode {

	public final static LWProjektAuftragsartCode NULL = new LWProjektAuftragsartCode(0, null, "", "");

//	public final static LWProjektAuftragsartCode C001 = new LWProjektAuftragsartCode(1, "001", "", "");
//	public final static LWProjektAuftragsartCode C002 = new LWProjektAuftragsartCode(2, "002", "", "");
//	public final static LWProjektAuftragsartCode C003 = new LWProjektAuftragsartCode(3, "003", "", "");

	LWProjektAuftragsartCode(final int id, final Object key,
		       		   final String shortText, final String longText) {
		super(id, key, shortText, longText);
	}

	LWProjektAuftragsartCode(final Object key, final String shortText, final String longText) {
		super(key, shortText, longText);
	}

}
