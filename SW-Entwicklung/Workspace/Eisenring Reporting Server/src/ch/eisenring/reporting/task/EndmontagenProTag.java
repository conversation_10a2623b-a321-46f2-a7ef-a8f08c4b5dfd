package ch.eisenring.reporting.task;

import ch.eisenring.app.shared.AbstractSoftwareComponent;
import ch.eisenring.app.shared.ServiceNotFoundException;
import ch.eisenring.core.collections.Map;
import ch.eisenring.core.collections.impl.HashMap;
import ch.eisenring.core.datatypes.date.TimestampUtil;
import ch.eisenring.core.datatypes.strings.Strings;
import ch.eisenring.core.logging.Logger;
import ch.eisenring.core.sort.Comparator;
import ch.eisenring.dms.service.DMSServiceException;
import ch.eisenring.dms.service.proxy.DMSDocumentHandle;
import ch.eisenring.email.EMail;
import ch.eisenring.jdbc.JDBCResultSet;
import ch.eisenring.jdbc.RowHandler;
import ch.eisenring.jdbc.StatementParameters;
import ch.eisenring.logiware.LWContextSource;
import ch.eisenring.logiware.LWProjektKey;
import ch.eisenring.logiware.code.soft.GSECode;
import ch.eisenring.lw.LWConstants;
import ch.eisenring.model.TransactionContext;
import ch.eisenring.reporting.report.kmem.KMEMFormular;
import ch.eisenring.reporting.report.kmem.KMEMProTagAuftragModel;
import ch.eisenring.reporting.report.kmem.KMEMReport;
import ch.eisenring.reporting.server.ReportingServerConfig;
import ch.eisenring.reporting.util.DMSHelper;
import ch.eisenring.reporting.util.DatabaseConstants;
import org.apache.poi.ss.usermodel.Workbook;

import java.io.File;
import java.io.FileOutputStream;
import java.io.IOException;
import java.io.OutputStream;
import java.sql.SQLException;
import java.time.LocalDate;
import java.time.ZoneOffset;
import java.util.*;
import java.util.concurrent.atomic.AtomicLong;
import java.util.concurrent.atomic.AtomicReference;
import java.util.stream.Collectors;

/**
 * Ist eine Zusammenfassung mehrer Abfragen in einer Liste.
 * Basis ist der Report Endmontage pro Küche in den Dispo-Client Werkzeugen @ReportEndmontagenRequestHandler.java
 */
public class EndmontagenProTag implements ReportingTask {

    private static String REPORT_NAME = "KM_EM_Liste";

    private AbstractSoftwareComponent server;
    private ReportingServerConfig config;
    private Date dateFrom = null;
    private Date dateUpto = null;

    @Override
    public Collection<EMail> execute(ReportingServerConfig config, AbstractSoftwareComponent server) throws IOException, DMSServiceException, ServiceNotFoundException {
        this.config = config;
        this.server = server;

        // Endmontagen zu den Aufträgen suchen
        Integer yearFrom = config.getInteger("YearFrom", 2020, 2099, LocalDate.now().getYear());
        dateFrom = Date.from(LocalDate.of(yearFrom.intValue(), 1, 1).atStartOfDay().toInstant(ZoneOffset.UTC));
        dateUpto = Date.from(LocalDate.of(LocalDate.now().getYear() + 1, 12, 31).atStartOfDay().toInstant(ZoneOffset.UTC));

        try {
            List<KMEMProTagAuftragModel> auftragList = calculateEndmontagenProKueche();

            Workbook workbook = KMEMReport.createReport(auftragList);

            DMSDocumentHandle documentHandle = saveReport(workbook);

            return createEmail(documentHandle);
        } catch (Exception e) {
            Logger.error("Die KM_EM_Liste konnte nicht erstellt werden: " + e.getMessage());
            Logger.error(e);
            throw e;
        }
    }

    public List<KMEMProTagAuftragModel> calculateEndmontagenProKueche() throws IOException {
        Map<LWProjektKey, KMEMProTagAuftragModel> auftragMap = new HashMap<>();
        final LWContextSource source = new LWContextSource(LWConstants.VERTRIEB) {
            @Override
            protected void loadImpl(final TransactionContext context) throws SQLException {
                // fetch 135'er
                final String sql1 = createAuftragQuery();
                final StatementParameters params1 = new StatementParameters(2);
                params1.addDate(dateFrom);
                params1.addDate(dateUpto);
                doQuery(sql1, params1, new RowHandler() {
                    @Override
                    public void handleRow(final JDBCResultSet resultSet) throws SQLException {
                        final String projektnummer = resultSet.getString(13);
                        final GSECode gse = GSECode.GSE_2501;
                        final KMEMProTagAuftragModel auftrag = new KMEMProTagAuftragModel(projektnummer, gse);
                        auftrag.basis = resultSet.getString(1);
                        auftrag.projektBezeichnung = resultSet.getString(2);
                        auftrag.tpl = resultSet.getString(4);
                        auftrag.kpl = resultSet.getString(7);
                        auftrag.wunschdatum = resultSet.getTimestamp(17);
                        auftrag.objektTyp = resultSet.getString(24);
                        auftrag.wunschdatumEM = resultSet.getTimestamp(31);
                        auftrag.planungsstatusEM = resultSet.getString(33);
                        auftrag.auftragEM = resultSet.getInteger(30);
                        auftrag.auftragKB = resultSet.getInteger(14);
                        auftrag.monteurKB = resultSet.getString(26);
                        auftrag.gu = resultSet.getString(10);
                        auftrag.bauart = resultSet.getString(22);
                        auftrag.disponentKB = resultSet.getString(28);
                        auftrag.disponentEM = resultSet.getString(35);
                        auftrag.infoMassaufnahme = resultSet.getString(18);
                        auftrag.bezugsDatum = resultSet.getTimestamp(29);
                        auftrag.monteurKBName = resultSet.getString(36);
                        auftrag.guEK = resultSet.getString(38);
                        auftrag.guRolle = resultSet.getString(41);
                        auftrag.guEKRolle = resultSet.getString(42);
                        auftragMap.put(auftrag.projektKey, auftrag);
                    }
                });

                // fetch and count 115'er
                final String sql2 = createEMQuery();
                final StatementParameters params2 = new StatementParameters(2);
                params2.addDate(dateFrom);
                params2.addDate(dateUpto);
                final AtomicLong lastTimestamp = new AtomicLong(TimestampUtil.NULL_TIMESTAMP);
                final AtomicReference<LWProjektKey> lastKey = new AtomicReference<>(LWProjektKey.INVALID);
                doQuery(sql2, params2, new RowHandler() {
                    @Override
                    public void handleRow(final JDBCResultSet resultSet) throws SQLException {
                        String pKey = resultSet.getString(1);
                        final GSECode gse = GSECode.GSE_2501;
                        final KMEMProTagAuftragModel auftrag = new KMEMProTagAuftragModel(pKey, gse);
                        KMEMProTagAuftragModel kbAuftrag = auftragMap.get(auftrag.projektKey);
                        if (kbAuftrag != null) {
                            KMEMFormular KMEMFormular = new KMEMFormular(pKey, gse);
                            KMEMFormular.formDate = resultSet.getTimestamp(4);
                            KMEMFormular.monteurCodeHard = resultSet.getString(5);
                            KMEMFormular.monteurCodeSoft = resultSet.getString(6);
                            kbAuftrag.emFormulare.add(KMEMFormular);
                        }
                        if (kbAuftrag != null && kbAuftrag.emFormulare.size() > 1) {
                            Collections.sort(kbAuftrag.emFormulare, new Comparator<KMEMFormular>() {
                                @Override
                                public int compare(final KMEMFormular f1, final KMEMFormular f2) {
                                    return f1.formDate.compareTo(f2.formDate);
                                }
                            });
                        }
                    }
                });

                final String sql3 = createAbnahmeQuery();
                final StatementParameters params3 = new StatementParameters(2);
                params3.addDate(dateFrom);
                params3.addDate(dateUpto);
                doQuery(sql3, params3, new RowHandler() {
                    @Override
                    public void handleRow(final JDBCResultSet resultSet) throws SQLException {
                        String pKey = resultSet.getString(1);
                        final GSECode gse = GSECode.GSE_2501;
                        final KMEMProTagAuftragModel auftrag = new KMEMProTagAuftragModel(pKey, gse);
                        KMEMProTagAuftragModel kbAuftrag = auftragMap.get(auftrag.projektKey);
                        if (kbAuftrag != null) {
                            kbAuftrag.abnahmen.add(resultSet.getTimestamp(3));
                        }
                    }
                });

                final String sql4 = createMontageAbschlussQuery();
                final StatementParameters params4 = new StatementParameters(2);
                params4.addDate(dateFrom);
                params4.addDate(dateUpto);
                doQuery(sql4, params4, new RowHandler() {
                    @Override
                    public void handleRow(final JDBCResultSet resultSet) throws SQLException {
                        Integer auftrag = resultSet.getInt(2);
                        Optional<KMEMProTagAuftragModel> kbAuftrag = auftragMap.values().stream().filter(a -> auftrag.equals(a.auftragKB)).findFirst();
                        // Wenn es einen offenen Montagescheine gibt
                        if (kbAuftrag.isPresent()) {
                            kbAuftrag.get().offenerMontagescheine.add(resultSet.getInteger(4));
                        }
                    }
                });

                final String sql5 = createLieferscheinQuery();
                final StatementParameters params5 = new StatementParameters(2);
                params5.addDate(dateFrom);
                params5.addDate(dateUpto);
                doQuery(sql5, params5, new RowHandler() {
                    @Override
                    public void handleRow(final JDBCResultSet resultSet) throws SQLException {
                        String pKey = resultSet.getString(1);
                        final GSECode gse = GSECode.GSE_2501;
                        final KMEMProTagAuftragModel auftrag = new KMEMProTagAuftragModel(pKey, gse);
                        KMEMProTagAuftragModel kbAuftrag = auftragMap.get(auftrag.projektKey);
                        // Wenn es einen offenen Lieferschein gibt
                        if (kbAuftrag != null) {
                            kbAuftrag.offenerLieferscheine.add(resultSet.getInteger(4));
                        }
                    }
                });

            }
        };
        final TransactionContext context = new TransactionContext();
        context.load(source);

        // aufträge sortieren nach anzahl endmontagen + projektnummer
        List<KMEMProTagAuftragModel> auftragModelList = auftragMap.values().stream().collect(Collectors.toList());

        Collections.sort(auftragModelList, new Comparator<KMEMProTagAuftragModel>() {
            @Override
            public int compare(final KMEMProTagAuftragModel i1, final KMEMProTagAuftragModel i2) {
                int r = -compareSigned(i1.emFormulare.size(), i2.emFormulare.size());
                if (r != 0)
                    return r;
                return Strings.compareIgnoreCase(i1.projektKey.getProjektnummer(), i2.projektKey.getProjektnummer());
            }
        });
        return auftragModelList;
    }

    private DMSDocumentHandle saveReport(Workbook workbook) throws IOException, DMSServiceException, ServiceNotFoundException {
        Integer folderId = config.getInteger("Folder", Integer.MIN_VALUE, Integer.MAX_VALUE, 0);
        File folder = new File(config.getExportPath());
        File file = new File(folder, new StringBuilder().append(REPORT_NAME).append(".xlsx").toString());

        try (OutputStream out = new FileOutputStream(file)) {
            workbook.write(out);
        } catch (IOException e) {
            Logger.error("Die KM_EM_Liste konnte nicht erstellt werden: " + e.getMessage());
            Logger.error(e);
            throw e;
        }

        try {
            DMSDocumentHandle documentHandle = DMSHelper.saveReport(server, file, folderId);
            return documentHandle;
        } catch (ServiceNotFoundException | DMSServiceException | IOException e) {
            Logger.error(e);
            Logger.error("Die KM_EM_Liste konnte nicht im DMS gespeichert werden: " + e.getMessage());
            throw e;
        } finally {
            // file.delete(); // Aktuell nicht gelöscht, falls es Probleme mit dem File gibt.
        }
    }

    private Collection<EMail> createEmail(DMSDocumentHandle documentHandle) {
        StringBuilder body = new StringBuilder();
        body.append("<html><body>");
        body.append("<p>Sehr geehrte Damen und Herren</p>");
        body.append("<p>Die Auswertung <a href=\"EisenringDMS://Goto/ObjectId/" + documentHandle.getPKValue() + "\"> KM_EM_Liste</a> wurde erstellt und im DMS aktualisiert</p>");
        body.append("</body></html>");
        // Email
        Collection<EMail> emailList = new ch.eisenring.core.collections.impl.ArrayList<>();
        Collection<String> emailAddresses = config.getEmailAddresses();
        if (emailAddresses != null) {
            for (String email : emailAddresses) {
                try {
                    EMail mail = new EMail();
                    mail.setFromName("HEAG Reporting");
                    mail.setFromAddress("<EMAIL>");
                    mail.setReplyTo("<EMAIL>");
                    mail.setSubject("Auswertung KM_EM_Liste");
                    mail.setPlainText(body.toString());
                    mail.addTo(email);
                    emailList.add(mail);
                } catch (Exception e) {
                    Logger.error(e);
                }
            }
        }
        return emailList;
    }

    private String createAuftragQuery() {
        return new StringBuilder() //
                .append("SELECT p.").append(DatabaseConstants.VertriebDboSchema.PAuftragTable.P_AUFTR_NR).append(" 'Basis Kommission'") //
                .append(", p.").append(DatabaseConstants.VertriebDboSchema.PAuftragTable.P_BEZ).append(" Projektbezeichung") //
                .append(", p.").append(DatabaseConstants.VertriebDboSchema.PAuftragTable.P_FAKT_PER_CD).append(" 'TPL Code'") //
                .append(", ap1.").append(DatabaseConstants.BasisDboSchema.AppcodeTable.CD_SOFT).append(" 'TPL Kurzzeichen'") //
                .append(", ap1.").append(DatabaseConstants.BasisDboSchema.AppcodeTable.BEZEICHNUNG).append(" 'TPL Name'") //
                .append(", p.").append(DatabaseConstants.VertriebDboSchema.PAuftragTable.P_GRUPPE).append(" 'KPL Code'") //
                .append(", akpl1.").append(DatabaseConstants.BasisDboSchema.AppcodeTable.CD_SOFT).append(" 'KPL Kurzzeichen'") //
                .append(", akpl1.").append(DatabaseConstants.BasisDboSchema.AppcodeTable.BEZEICHNUNG).append(" 'KPL Name'") //
                .append(", s1.").append(DatabaseConstants.BasisDboSchema.SubjektTable.ID).append(" 'ID GU'") //
                .append(", s1.").append(DatabaseConstants.BasisDboSchema.SubjektTable.NAME).append(" 'Name GU'") //
                .append(", s1.").append(DatabaseConstants.BasisDboSchema.SubjektTable.ZUSATZ_NAME).append(" 'Zusatzname GU'") //
                .append(", s1.").append(DatabaseConstants.BasisDboSchema.SubjektTable.ZUSATZ_BEZ).append(" 'ZusatzBez GU'") //
                .append(", k.").append(DatabaseConstants.VertriebDboSchema.PAuftragTable.P_AUFTR_NR).append(" Kommission") //
                .append(", a.").append(DatabaseConstants.VertriebDboSchema.AuftragTable.AUFTR_NR) //
                .append(", a.").append(DatabaseConstants.VertriebDboSchema.AuftragTable.ABW_ART) //
                .append(", aa1.").append(DatabaseConstants.BasisDboSchema.AppcodeTable.BEZEICHNUNG).append(" 'AbwArt Bezeichnung'") //
                .append(", a.").append(DatabaseConstants.VertriebDboSchema.AuftragTable.DAT_WU).append(" 'Wunschdatum'") //
                .append(", ak.").append(DatabaseConstants.VertriebDboSchema.AuftragsKopfZusatzTable.INFO_MASSE).append(" 'Info Masse'") // Info Massaufnahme
                .append(", ak.").append(DatabaseConstants.VertriebDboSchema.AuftragsKopfZusatzTable.PLANUNG).append(" 'Code Küchenstatus'") //
                .append(", aak3.").append(DatabaseConstants.BasisDboSchema.AppcodeTable.BEZEICHNUNG).append(" 'Bezeichnung Küchenstatus'") //
                .append(", ak.").append(DatabaseConstants.VertriebDboSchema.AuftragsKopfZusatzTable.SCH_BLOCK1).append(" 'Code Bewohnt' ") //
                .append(", aak1.").append(DatabaseConstants.BasisDboSchema.AppcodeTable.BEZEICHNUNG).append(" 'Bezeichnung Bewohnt'") //
                .append(", ak.").append(DatabaseConstants.VertriebDboSchema.AuftragsKopfZusatzTable.SCH_BLOCK2).append(" 'Code Objektart'") //
                .append(", aak2.").append(DatabaseConstants.BasisDboSchema.AppcodeTable.BEZEICHNUNG).append(" 'Bezeichnung Objektart'") //
                .append(", ak.").append(DatabaseConstants.VertriebDboSchema.AuftragsKopfZusatzTable.MONTEUR_SIR).append(" 'Code Monteur KB'") //
                .append(", aak5.").append(DatabaseConstants.BasisDboSchema.AppcodeTable.CD_SOFT).append(" 'Code Soft Monteur KB'") //
                .append(", ak.").append(DatabaseConstants.VertriebDboSchema.AuftragsKopfZusatzTable.DISPONENTSIR).append(" 'Disponent Code'")
                .append(", aak4.").append(DatabaseConstants.BasisDboSchema.AppcodeTable.BEZEICHNUNG).append(" 'Disponent Name'") //
                .append(", ak.").append(DatabaseConstants.VertriebDboSchema.AuftragsKopfZusatzTable.M_DATUM4).append(" 'Bezugsdatum'")
                .append(", em.").append(DatabaseConstants.VertriebDboSchema.AuftragTable.AUFTR_NR).append(" 'Auftrag EM'") //
                .append(", em.").append(DatabaseConstants.VertriebDboSchema.AuftragTable.DAT_WU).append(" 'Wunschdatum'") //
                .append(", emk.").append(DatabaseConstants.VertriebDboSchema.AuftragsKopfZusatzTable.PLANUNG).append(" 'Code Küchenstatus EM'")
                .append(", aemk2.").append(DatabaseConstants.BasisDboSchema.AppcodeTable.BEZEICHNUNG).append(" 'Bezeichnung Küchenstatus EM'") //
                .append(", emk.").append(DatabaseConstants.VertriebDboSchema.AuftragsKopfZusatzTable.DISPONENTSIR).append(" 'Disponent EM'")
                .append(", aemk3.").append(DatabaseConstants.BasisDboSchema.AppcodeTable.BEZEICHNUNG).append(" 'Bezeichnung Disponent EM'") //
                .append(", aak5.").append(DatabaseConstants.BasisDboSchema.AppcodeTable.BEZEICHNUNG).append(" 'Bezeichnung Monteur KB'") //
                .append(", s2.").append(DatabaseConstants.BasisDboSchema.SubjektTable.ID).append(" 'ID GU EK'") //
                .append(", s2.").append(DatabaseConstants.BasisDboSchema.SubjektTable.NAME).append(" 'Name GU EK'") //
                .append(", s2.").append(DatabaseConstants.BasisDboSchema.SubjektTable.ZUSATZ_NAME).append(" 'Zusatzname GU EK'") //
                .append(", s2.").append(DatabaseConstants.BasisDboSchema.SubjektTable.ZUSATZ_BEZ).append(" 'ZusatzBez GU EK'") //
                .append(", p.").append(DatabaseConstants.VertriebDboSchema.PAuftragTable.ADRESSART_I).append(" 'Addressart I'") // Zu Subjekt_I s1 --> Rolle 004 = Generalunternehmer
                .append(", p.").append(DatabaseConstants.VertriebDboSchema.PAuftragTable.ADRESSART_A).append(" 'Addressart A'") // Zu Subjekt_A s2 --> Rolle 004 = Generalunternehmer

                .append(" FROM ").append(DatabaseConstants.VertriebDboSchema.AuftragTable._TN).append(" a") //
                //.append(" WITH (nolock)")

                .append(" LEFT JOIN ").append(DatabaseConstants.BasisDboSchema.AppcodeTable._TN).append(" aa1") //
                .append(" ON aa1.").append(DatabaseConstants.BasisDboSchema.AppcodeTable.CD_HARD).append(" = a.").append(DatabaseConstants.VertriebDboSchema.AuftragTable.ABW_ART) //
                .append(" AND aa1.").append(DatabaseConstants.BasisDboSchema.AppcodeTable.COL_NAME).append(" = 'ABWART'") //
                .append(" AND aa1.").append(DatabaseConstants.BasisDboSchema.AppcodeTable.CD_SPRACHE).append(" = '001'") //

                .append(" JOIN ").append(DatabaseConstants.VertriebDboSchema.AuftragsKopfZusatzTable._TN).append(" ak") //
                .append(" ON ak.").append(DatabaseConstants.VertriebDboSchema.AuftragsKopfZusatzTable.AUFTR_NR).append(" = a.").append(DatabaseConstants.VertriebDboSchema.AuftragTable.AUFTR_NR) //
                .append(" AND ak.").append(DatabaseConstants.VertriebDboSchema.AuftragsKopfZusatzTable.ID_GSE).append(" = a.").append(DatabaseConstants.VertriebDboSchema.AuftragTable.ID_GSE) //

                .append(" LEFT JOIN ").append(DatabaseConstants.BasisDboSchema.AppcodeTable._TN).append(" aak1") //
                .append(" ON aak1.").append(DatabaseConstants.BasisDboSchema.AppcodeTable.CD_HARD).append(" = ltrim(rtrim(ak.").append(DatabaseConstants.VertriebDboSchema.AuftragsKopfZusatzTable.SCH_BLOCK1).append("))") //
                .append(" AND aak1.").append(DatabaseConstants.BasisDboSchema.AppcodeTable.COL_NAME).append(" = 'SCH_BLOCK1'") //
                .append(" AND aak1.").append(DatabaseConstants.BasisDboSchema.AppcodeTable.CD_SPRACHE).append(" = '001'") //

                .append(" LEFT JOIN ").append(DatabaseConstants.BasisDboSchema.AppcodeTable._TN).append(" aak2") //
                .append(" ON aak2.").append(DatabaseConstants.BasisDboSchema.AppcodeTable.CD_HARD).append(" = ltrim(rtrim(ak.").append(DatabaseConstants.VertriebDboSchema.AuftragsKopfZusatzTable.SCH_BLOCK2).append("))") //
                .append(" AND aak2.").append(DatabaseConstants.BasisDboSchema.AppcodeTable.COL_NAME).append(" = 'SCH_BLOCK2'") //
                .append(" AND aak2.").append(DatabaseConstants.BasisDboSchema.AppcodeTable.CD_SPRACHE).append(" = '001'") //

                .append(" LEFT JOIN ").append(DatabaseConstants.BasisDboSchema.AppcodeTable._TN).append(" aak3") //
                .append(" ON aak3.").append(DatabaseConstants.BasisDboSchema.AppcodeTable.CD_HARD).append(" = ltrim(rtrim(ak.").append(DatabaseConstants.VertriebDboSchema.AuftragsKopfZusatzTable.PLANUNG).append("))") //
                .append(" AND aak3.").append(DatabaseConstants.BasisDboSchema.AppcodeTable.COL_NAME).append(" = 'PLANUNG'") //
                .append(" AND aak3.").append(DatabaseConstants.BasisDboSchema.AppcodeTable.CD_SPRACHE).append(" = '001'") //

                .append(" LEFT JOIN ").append(DatabaseConstants.BasisDboSchema.AppcodeTable._TN).append(" aak4") //
                .append(" ON aak4.").append(DatabaseConstants.BasisDboSchema.AppcodeTable.CD_HARD).append(" = ltrim(rtrim(ak.").append(DatabaseConstants.VertriebDboSchema.AuftragsKopfZusatzTable.DISPONENTSIR).append("))") //
                .append(" AND aak4.").append(DatabaseConstants.BasisDboSchema.AppcodeTable.COL_NAME).append(" = 'DISPONENTSIR'") //
                .append(" AND aak4.").append(DatabaseConstants.BasisDboSchema.AppcodeTable.CD_SPRACHE).append(" = '001'") //

                .append(" LEFT JOIN ").append(DatabaseConstants.BasisDboSchema.AppcodeTable._TN).append(" aak5") //
                .append(" ON aak5.").append(DatabaseConstants.BasisDboSchema.AppcodeTable.CD_HARD).append(" = ltrim(rtrim(ak.").append(DatabaseConstants.VertriebDboSchema.AuftragsKopfZusatzTable.MONTEUR_SIR).append("))") //
                .append(" AND aak5.").append(DatabaseConstants.BasisDboSchema.AppcodeTable.COL_NAME).append(" = 'MONTEURSIR'") //
                .append(" AND aak5.").append(DatabaseConstants.BasisDboSchema.AppcodeTable.CD_SPRACHE).append(" = '001'") //

                .append(" JOIN ").append(DatabaseConstants.VertriebDboSchema.PAuftragTable._TN).append(" k") // Join auf Kommission
                .append(" ON k.").append(DatabaseConstants.VertriebDboSchema.PAuftragTable.P_AUFTR_NR).append(" = a.").append(DatabaseConstants.VertriebDboSchema.AuftragTable.P_AUFTR_NR) //

                .append(" JOIN ").append(DatabaseConstants.VertriebDboSchema.PAuftragTable._TN).append(" p") //
                .append(" ON p.").append(DatabaseConstants.VertriebDboSchema.PAuftragTable.P_AUFTR_NR).append(" = k.").append(DatabaseConstants.VertriebDboSchema.PAuftragTable.P_AUFTRNR_B) //

                .append(" LEFT JOIN ").append(DatabaseConstants.BasisDboSchema.AppcodeTable._TN).append(" ap1") //
                .append(" ON ap1.").append(DatabaseConstants.BasisDboSchema.AppcodeTable.CD_HARD).append(" = p.").append(DatabaseConstants.VertriebDboSchema.PAuftragTable.P_FAKT_PER_CD) //
                .append(" AND ap1.").append(DatabaseConstants.BasisDboSchema.AppcodeTable.COL_NAME).append(" = 'PFAKTPERCD'") //
                .append(" AND ap1.").append(DatabaseConstants.BasisDboSchema.AppcodeTable.CD_SPRACHE).append(" = '001'") //

                .append(" LEFT JOIN ").append(DatabaseConstants.BasisDboSchema.AppcodeTable._TN).append(" akpl1") //
                .append(" ON akpl1.").append(DatabaseConstants.BasisDboSchema.AppcodeTable.CD_HARD).append(" = p.").append(DatabaseConstants.VertriebDboSchema.PAuftragTable.P_GRUPPE) //
                .append(" AND akpl1.").append(DatabaseConstants.BasisDboSchema.AppcodeTable.COL_NAME).append(" = 'PFAKTPERCD'") //
                .append(" AND akpl1.").append(DatabaseConstants.BasisDboSchema.AppcodeTable.CD_SPRACHE).append(" = '001'") //

                .append(" LEFT JOIN ").append(DatabaseConstants.BasisDboSchema.AppcodeTable._TN).append(" ap2") //
                .append(" ON ap2.").append(DatabaseConstants.BasisDboSchema.AppcodeTable.CD_HARD).append(" = p.").append(DatabaseConstants.VertriebDboSchema.PAuftragTable.BASISOBJEKT) //
                .append(" AND ap2.").append(DatabaseConstants.BasisDboSchema.AppcodeTable.COL_NAME).append(" = 'BASISOBJEKT'") //
                .append(" AND ap2.").append(DatabaseConstants.BasisDboSchema.AppcodeTable.CD_SPRACHE).append(" = '001'") //

                .append(" LEFT JOIN ").append(DatabaseConstants.BasisDboSchema.SubjektTable._TN).append(" s1") //
                .append(" ON s1.").append(DatabaseConstants.BasisDboSchema.SubjektTable.ID).append(" = p.").append(DatabaseConstants.VertriebDboSchema.PAuftragTable.ID_SUBJEKT_I) //

                .append(" LEFT JOIN ").append(DatabaseConstants.BasisDboSchema.SubjektTable._TN).append(" s2") //
                .append(" ON s2.").append(DatabaseConstants.BasisDboSchema.SubjektTable.ID).append(" = p.").append(DatabaseConstants.VertriebDboSchema.PAuftragTable.ID_SUBJEKT_A) //

                .append(" LEFT JOIN ").append(DatabaseConstants.VertriebDboSchema.AuftragTable._TN).append(" em") //
                .append(" ON a.").append(DatabaseConstants.VertriebDboSchema.AuftragTable.P_AUFTR_NR).append(" = em.").append(DatabaseConstants.VertriebDboSchema.AuftragTable.P_AUFTR_NR) //
                .append(" and a.").append(DatabaseConstants.VertriebDboSchema.AuftragTable.ID_GSE).append(" = em.").append(DatabaseConstants.VertriebDboSchema.AuftragTable.ID_GSE) //
                .append(" and em.").append(DatabaseConstants.VertriebDboSchema.AuftragTable.ABW_ART).append(" = '115'") //
                .append(" and em.").append(DatabaseConstants.VertriebDboSchema.AuftragTable.STATUS).append(" != '099'") //

                .append(" LEFT JOIN ").append(DatabaseConstants.VertriebDboSchema.AuftragsKopfZusatzTable._TN).append(" emk") //
                .append(" ON em.").append(DatabaseConstants.VertriebDboSchema.AuftragTable.AUFTR_NR).append(" = emk.").append(DatabaseConstants.VertriebDboSchema.AuftragsKopfZusatzTable.AUFTR_NR) //
                .append(" AND em.").append(DatabaseConstants.VertriebDboSchema.AuftragTable.ID_GSE).append(" = emk.").append(DatabaseConstants.VertriebDboSchema.AuftragsKopfZusatzTable.ID_GSE) //

                .append(" LEFT JOIN ").append(DatabaseConstants.BasisDboSchema.AppcodeTable._TN).append(" aemk2") //
                .append(" ON aemk2.").append(DatabaseConstants.BasisDboSchema.AppcodeTable.CD_HARD).append(" = ltrim(rtrim(emk.").append(DatabaseConstants.VertriebDboSchema.AuftragsKopfZusatzTable.PLANUNG).append("))") //
                .append(" AND aemk2.").append(DatabaseConstants.BasisDboSchema.AppcodeTable.COL_NAME).append(" = 'PLANUNG'") //
                .append(" AND aemk2.").append(DatabaseConstants.BasisDboSchema.AppcodeTable.CD_SPRACHE).append(" = '001'") //

                .append(" LEFT JOIN ").append(DatabaseConstants.BasisDboSchema.AppcodeTable._TN).append(" aemk3") //
                .append(" ON aemk3.").append(DatabaseConstants.BasisDboSchema.AppcodeTable.CD_HARD).append(" = ltrim(rtrim(emk.").append(DatabaseConstants.VertriebDboSchema.AuftragsKopfZusatzTable.DISPONENTSIR).append("))") //
                .append(" AND aemk3.").append(DatabaseConstants.BasisDboSchema.AppcodeTable.COL_NAME).append(" = 'DISPONENTSIR'") //
                .append(" AND aemk3.").append(DatabaseConstants.BasisDboSchema.AppcodeTable.CD_SPRACHE).append(" = '001'") //

                .append(" WHERE a.").append(DatabaseConstants.VertriebDboSchema.AuftragTable.ID_GSE).append(" = '2501'") //
                .append(" AND a.").append(DatabaseConstants.VertriebDboSchema.AuftragTable.STATUS).append(" != '099'") //
                .append(" AND a.").append(DatabaseConstants.VertriebDboSchema.AuftragTable.DAT_WU).append(" >= ").append(" ? ") //
                .append(" AND a.").append(DatabaseConstants.VertriebDboSchema.AuftragTable.DAT_WU).append(" <= ").append(" ? ") //
               // .append(" AND a.").append(DatabaseConstants.VertriebDboSchema.AuftragTable.P_AUFTR_NR).append(" in ('190145')") //
                .append(" AND a.").append(DatabaseConstants.VertriebDboSchema.AuftragTable.ABW_ART).append(" in ").append("('132','135')") //
                .toString();
    }

    public String createEMQuery() {
        return new StringBuilder() //
                .append("Select  f.PAuftrNr, f.AuftrNr, f.FormArt, f.FormDatum, fm.MonteurSir, ac.cd_soft " +
                        " from vertrieb.dbo.FORMULAR f " +
                        " join basis.dbo.DOKUMENT d " +
                        " on f.FormNr = d.FormNr " +
                        " and f.FormArt = d.FormArt " +
                        " and f.Id_GSE = d.Id_GSE " +
                        " join basis.dbo.FMIndivRep fm " +
                        " on d.DokId = fm.DokId " +
                        " left join basis.dbo.Appcode ac " +
                        " on ac.cd_hard = ltrim(rtrim(fm.MonteurSir)) " +
                        " and ac.Col_Name = 'MONTEURSIR' " +
                        " and  ac.CD_Sprache = '001' " +
                        " where f.FormArt in ('204') " +
                        " and  f.Id_GSE = '2501' " +
                        " and f.FortStatus = '001' " +
                        " and f.AbwArt in ('115') " +
                        " and f.Upd_Sabe in ('dbo', 'brickworks', 'mobileap') " +
                        " and f.FormDatum >= ? " +
                        " AND f.FormDatum <= ?")
                .toString();
    }

    public String createAbnahmeQuery() {
        return new StringBuilder() //
                .append("select PAuftrNr, AuftrNr, formDatum" +
                        " from FORMULAR" +
                        " where FormArt = '501'" +
                        "  and AbwArt in ('135')" + // Todo check whether to add 132
                        "  and FormDatum >= ?" +
                        "  AND FormDatum <= ?" +
                        "  and Add_Sabe = 'JournalAp'")
                .toString();
    }

    /**
     * FotStatus = 000 --> Offener Montageabschluss
     * @return
     */
    public String createMontageAbschlussQuery() {
        return new StringBuilder() //
                .append("select PAuftrNr, AuftrNr, formDatum, FormNr" +
                        " from FORMULAR" +
                        " where FormArt = '001'" +
                        " and AbwArt in ('135')" +
                        " and FortStatus = '000'" +
                        " and FormDatum >= ?" +
                        " AND FormDatum <= ?"
                )
                .toString();
    }

    /**
     * FortStatus = 000 --> Offener Lieferschein
     * @return
     */
    public String createLieferscheinQuery() {
        return new StringBuilder() //
                .append("select PAuftrNr, AuftrNr, formDatum, FormNr" +
                        " from FORMULAR" +
                        " where FormArt = '204'" +
                        "  and AbwArt in ('115', '129', '131', '132', '133', '135', '137')" +
                        " and FortStatus = '000'" +
                        "  and FormDatum >= ?" +
                        "  AND FormDatum <= ?"
                )
                .toString();
    }

    public static void main(String[] args) {
        System.out.println(new EndmontagenProTag().createAuftragQuery());
    }

}