package ch.eisenring.reporting.report.kmem;

import ch.eisenring.core.StringUtil;
import ch.eisenring.core.datatypes.date.TimestampUtil;
import ch.eisenring.core.poi.POIUtil;
import ch.eisenring.logiware.code.hard.LWObjektTyp;
import ch.eisenring.logiware.code.soft.LWGranit2Code;
import ch.eisenring.reporting.report.Column;
import ch.eisenring.reporting.util.CellStyleManager;
import ch.eisenring.reporting.util.ExcelHelper;
import org.apache.commons.lang3.StringUtils;
import org.apache.poi.ss.usermodel.*;
import org.apache.poi.ss.util.CellRangeAddress;

import java.text.SimpleDateFormat;
import java.util.Collections;
import java.util.Date;
import java.util.List;
import java.util.Set;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.stream.Collectors;

import static ch.eisenring.reporting.report.kmem.KMEMReport.Columns.*;
import static ch.eisenring.reporting.util.CellStyleManager.CellStyleName.*;

public class KMEMReport {
    private static String DATE_FORMAT = "dd.MM.yyyy";

    private static String SHEET_AUSWERTUNG = "Auswertung";

    public interface Columns {
        // Define columns with dynamically calculated positions
        AtomicInteger positionIndex = new AtomicInteger();
        Column BASIS = new Column(positionIndex.getAndIncrement(), "Basis", 20);
        Column PROJEKT = new Column(positionIndex.getAndIncrement(), "Projekt", 20);
        Column PROJEKT_BEZEICHNUNG = new Column(positionIndex.getAndIncrement(), "Projekt Bezeichnung", 100);
        Column TPL = new Column(positionIndex.getAndIncrement(), "TPL");
        Column KPL = new Column(positionIndex.getAndIncrement(), "KPL");
        Column WUNSCHDATUM_KB = new Column(positionIndex.getAndIncrement(), "Wunschdatum KM", CellType.NUMERIC);
        Column OBJEKT_TYP = new Column(positionIndex.getAndIncrement(), "Objekttyp", 24);
        Column WUNSCHDATUM_EM = new Column(positionIndex.getAndIncrement(), "Wunschdat. EM", CellType.NUMERIC);
        Column PLANUNGSTATUS_EM = new Column(positionIndex.getAndIncrement(), "Planungsstatus EM");
        Column BELEG_EM = new Column(positionIndex.getAndIncrement(), "Endmontagen Belegnr.", 24, CellType.NUMERIC);
        Column KM_MONT = new Column(positionIndex.getAndIncrement(), "KM Mont");
        Column KM_MONT_NAME = new Column(positionIndex.getAndIncrement(), "KM Monteur Name", 48);
        Column EM_MONT = new Column(positionIndex.getAndIncrement(), "EM Monteure", 72);
        Column ANZAHL_MONT = new Column(positionIndex.getAndIncrement(), "Anzahl Monteure", CellType.NUMERIC);
        Column ANZAHL_EM = new Column(positionIndex.getAndIncrement(), "Anzahl EM", CellType.NUMERIC);
        Column BEZUGDATUM = new Column(positionIndex.getAndIncrement(), "Bezug/Übergabe", CellType.NUMERIC);
        Column ZEIT_EM = new Column(positionIndex.getAndIncrement(), "Endmontagezeit", CellType.NUMERIC);
        Column GU = new Column(positionIndex.getAndIncrement(), "GU", 56);
        Column BAUART = new Column(positionIndex.getAndIncrement(), "Bauart", 24);
        Column DISPONENT_EM = new Column(positionIndex.getAndIncrement(), "Disponent EM", 48);
        Column DISPONENT_KM = new Column(positionIndex.getAndIncrement(), "Disponent KM", 48);
        Column INFO_MASSAUFNAHME = new Column(positionIndex.getAndIncrement(), "Info Masse", 72);
        Column DATUM_ABNAHME = new Column(positionIndex.getAndIncrement(), "FormDatu Abnahme", 18, CellType.NUMERIC);
        Column OFFENE_MONTAGEABSCHLUSS = new Column(positionIndex.getAndIncrement(), "Offene Montageabschlüsse", 60);
        Column OFFENE_LIEFERSCHEINE = new Column(positionIndex.getAndIncrement(), "Offene Lieferscheine", 60);

    }

    public static Workbook createReport(List<KMEMProTagAuftragModel> auftragModelList) {
        Workbook workbook = POIUtil.createWorkbook();

        CellStyleManager cellStyleManager = new CellStyleManager(workbook);

        Sheet sht1 = workbook.createSheet(SHEET_AUSWERTUNG);

        // Freeze first row
        sht1.createFreezePane(0, 1);

        AtomicInteger rowIndex = new AtomicInteger();
        // ColumnHeaderRow
        Row row = sht1.createRow(rowIndex.getAndIncrement());
        final int columnCount = Columns.positionIndex.get();
        for (int i = 0; i < columnCount; i++) {
            Cell cell = row.createCell(i);
            cell.setCellStyle(cellStyleManager.getStyle(HEADER));
            setupHeaderCell(cell, i);
        }

        // The EM are added after the other columns
        /*
        int maxEMS = auftragModelList.stream().mapToInt(a -> a.emFormulare.size()).max().orElse(1);
        int emColumnStart = columnCount;
        for (int i = 1; i <= maxEMS; i++) {
            Cell cell1 = row.createCell(emColumnStart);
            cell1.setCellStyle(headerStyle);
            cell1.setCellValue(i + ". EM");
            sht1.setColumnWidth(emColumnStart, 16 * 256);
            emColumnStart++;
            Cell cell2 = row.createCell(emColumnStart);
            cell2.setCellStyle(headerStyle);
            cell2.setCellValue("EM-Mo " + i);
            sht1.setColumnWidth(emColumnStart, 18 * 256);
            emColumnStart++;
        }
        */
        // Data
        for (KMEMProTagAuftragModel auftrag : auftragModelList) {
            Row dataRow = sht1.createRow(rowIndex.getAndIncrement());
            for (int columnIndex = 0; columnIndex < columnCount; columnIndex++) {
                setupDataCell(dataRow, cellStyleManager, columnIndex, auftrag);
            }
            // EMs
            /*
            emColumnStart = columnCount;
            for (KMEMFormular KMEMFormular : auftrag.emFormulare) {
                Cell emDateCell = dataRow.createCell(emColumnStart);
                emDateCell.setCellStyle(dataStyle);
                emDateCell.setCellValue(new SimpleDateFormat(DATE_FORMAT).format(KMEMFormular.formDate));
                emColumnStart++;
                Cell emMontCell = dataRow.createCell(emColumnStart);
                emMontCell.setCellStyle(dataStyle);

                emMontCell.setCellValue(KMEMFormular.monteurCodeSoft);
                emColumnStart++;
            }

             */
        }

        // set Column width
        for (int columnIndex = 0; columnIndex < columnCount; columnIndex++) {
            sht1.autoSizeColumn(columnIndex);
        }

        sht1.setAutoFilter(new CellRangeAddress(0, rowIndex.get() - 1, 0, columnCount - 1));

        return workbook;
    }



    private static void setupHeaderCell(Cell cell, int columnIndex) {
        if (columnIndex == BASIS.getPosition()) {
            String value = BASIS.getHeader();
            if (value == null) {
                cell.setCellValue("NULL");
            } else {
                cell.setCellValue(value);
            }
        } else if (columnIndex == PROJEKT.getPosition()) {
            String value = PROJEKT.getHeader();
            if (value == null) {
                cell.setCellValue("NULL");
            } else {
                cell.setCellValue(value);
            }
        } else if (columnIndex == PROJEKT_BEZEICHNUNG.getPosition()) {
            String value = PROJEKT_BEZEICHNUNG.getHeader();
            if (value == null) {
                cell.setCellValue("NULL");
            } else {
                cell.setCellValue(value);
            }
        } else if (columnIndex == TPL.getPosition()) {
            String value = TPL.getHeader();
            if (value == null) {
                cell.setCellValue("NULL");
            } else {
                cell.setCellValue(value);
            }
        } else if (columnIndex == KPL.getPosition()) {
            String value = KPL.getHeader();
            if (value == null) {
                cell.setCellValue("NULL");
            } else {
                cell.setCellValue(value);
            }
        } else if (columnIndex == WUNSCHDATUM_KB.getPosition()) {
            String value = WUNSCHDATUM_KB.getHeader();
            if (value == null) {
                cell.setCellValue("NULL");
            } else {
                cell.setCellValue(value);
            }
        } else if (columnIndex == OBJEKT_TYP.getPosition()) {
            String value = OBJEKT_TYP.getHeader();
            if (value == null) {
                cell.setCellValue("NULL");
            } else {
                cell.setCellValue(value);
            }
        } else if (columnIndex == WUNSCHDATUM_EM.getPosition()) {
            String value = WUNSCHDATUM_EM.getHeader();
            if (value == null) {
                cell.setCellValue("NULL");
            } else {
                cell.setCellValue(value);
            }
        } else if (columnIndex == PLANUNGSTATUS_EM.getPosition()) {
            String value = PLANUNGSTATUS_EM.getHeader();
            if (value == null) {
                cell.setCellValue("NULL");
            } else {
                cell.setCellValue(value);
            }
        } else if (columnIndex == BELEG_EM.getPosition()) {
            String value = BELEG_EM.getHeader();
            if (value == null) {
                cell.setCellValue("NULL");
            } else {
                cell.setCellValue(value);
            }
        } else if (columnIndex == KM_MONT.getPosition()) {
            String value = KM_MONT.getHeader();
            if (value == null) {
                cell.setCellValue("NULL");
            } else {
                cell.setCellValue(value);
            }
        } else if (columnIndex == EM_MONT.getPosition()) {
            String value = EM_MONT.getHeader();
            if (value == null) {
                cell.setCellValue("NULL");
            } else {
                cell.setCellValue(value);
            }
        } else if (columnIndex == ANZAHL_MONT.getPosition()) {
            String value = ANZAHL_MONT.getHeader();
            if (value == null) {
                cell.setCellValue("NULL");
            } else {
                cell.setCellValue(value);
            }
        } else if (columnIndex == ANZAHL_EM.getPosition()) {
            String value = ANZAHL_EM.getHeader();
            if (value == null) {
                cell.setCellValue("NULL");
            } else {
                cell.setCellValue(value);
            }
        } else if (columnIndex == BEZUGDATUM.getPosition()) {
            String value = BEZUGDATUM.getHeader();
            if (value == null) {
                cell.setCellValue("NULL");
            } else {
                cell.setCellValue(value);
            }
        } else if (columnIndex == ZEIT_EM.getPosition()) {
            String value = ZEIT_EM.getHeader();
            if (value == null) {
                cell.setCellValue("NULL");
            } else {
                cell.setCellValue(value);
            }
        } else if (columnIndex == GU.getPosition()) {
            String value = GU.getHeader();
            if (value == null) {
                cell.setCellValue("NULL");
            } else {
                cell.setCellValue(value);
            }
        } else if (columnIndex == BAUART.getPosition()) {
            String value = BAUART.getHeader();
            if (value == null) {
                cell.setCellValue("NULL");
            } else {
                cell.setCellValue(value);
            }
        } else if (columnIndex == DISPONENT_EM.getPosition()) {
            String value = DISPONENT_EM.getHeader();
            if (value == null) {
                cell.setCellValue("NULL");
            } else {
                cell.setCellValue(value);
            }
        }
         else if (columnIndex == DISPONENT_KM.getPosition()) {
            String value = DISPONENT_KM.getHeader();
            if (value == null) {
                cell.setCellValue("NULL");
            } else {
                cell.setCellValue(value);
            }
        } else if (columnIndex == INFO_MASSAUFNAHME.getPosition()) {
            String value = INFO_MASSAUFNAHME.getHeader();
            if (value == null) {
                cell.setCellValue("NULL");
            } else {
                cell.setCellValue(value);
            }
        } else if (columnIndex == DATUM_ABNAHME.getPosition()) {
            String value = DATUM_ABNAHME.getHeader();
            if (value == null) {
                cell.setCellValue("NULL");
            } else {
                cell.setCellValue(value);
            }
        } else if (columnIndex == OFFENE_MONTAGEABSCHLUSS.getPosition()) {
            String value = OFFENE_MONTAGEABSCHLUSS.getHeader();
            if (value == null) {
                cell.setCellValue("NULL");
            } else {
                cell.setCellValue(value);
            }
        } else if (columnIndex == KM_MONT_NAME.getPosition()) {
            String value = KM_MONT_NAME.getHeader();
            if (value == null) {
                cell.setCellValue("NULL");
            } else {
                cell.setCellValue(value);
            }
        } else if (columnIndex == OFFENE_LIEFERSCHEINE.getPosition()) {
            String value = OFFENE_LIEFERSCHEINE.getHeader();
            if (value == null) {
                cell.setCellValue("NULL");
            } else {
                cell.setCellValue(value);
            }
        } else {
            cell.setCellValue("Unbekannte Spalte");
        }
    }

    private static void setupDataCell(Row row, CellStyleManager cellStyleManager, int columnIndex, KMEMProTagAuftragModel auftrag) {
        if (columnIndex == BASIS.getPosition()) {
            Cell cell = row.createCell(columnIndex, BASIS.getCellType());
            cell.setCellStyle(cellStyleManager.getStyle(TEXT));
            String value = auftrag.basis;
            if (value == null) {
                cell.setCellValue("");
            } else {
                cell.setCellValue(value);
            }
        } else if (columnIndex == PROJEKT.getPosition()) {
            Cell cell = row.createCell(columnIndex, PROJEKT.getCellType());
            cell.setCellStyle(cellStyleManager.getStyle(TEXT));
            String value = auftrag.projektKey.getProjektnummer();
            if (value == null) {
                cell.setCellValue("");
            } else {
                cell.setCellValue(value);
            }
        } else if (columnIndex == PROJEKT_BEZEICHNUNG.getPosition()) {
            Cell cell = row.createCell(columnIndex, PROJEKT_BEZEICHNUNG.getCellType());
            cell.setCellStyle(cellStyleManager.getStyle(DATA));
            String value = auftrag.projektBezeichnung;
            if (value == null) {
                cell.setCellValue("");
            } else {
                cell.setCellValue(value.replaceAll("\\r?\\n", " "));
            }
        } else if (columnIndex == TPL.getPosition()) {
            Cell cell = row.createCell(columnIndex, TPL.getCellType());
            cell.setCellStyle(cellStyleManager.getStyle(DATA));
            String value = auftrag.tpl;
            if (value == null) {
                cell.setCellValue("");
            } else {
                cell.setCellValue(value);
            }
        } else if (columnIndex == KPL.getPosition()) {
            Cell cell = row.createCell(columnIndex, KPL.getCellType());
            cell.setCellStyle(cellStyleManager.getStyle(DATA));
            String value = auftrag.kpl;
            if (value == null) {
                cell.setCellValue("");
            } else {
                cell.setCellValue(value);
            }
        } else if (columnIndex == WUNSCHDATUM_KB.getPosition()) {
            Cell cell = row.createCell(columnIndex, WUNSCHDATUM_KB.getCellType());
            cell.setCellStyle(cellStyleManager.getStyle(DATE));
            Date value = auftrag.wunschdatum;
            if (value == null) {
                cell.setCellValue("");
            } else {
                cell.setCellValue(value);
            }
        } else if (columnIndex == OBJEKT_TYP.getPosition()) {
            Cell cell = row.createCell(columnIndex, OBJEKT_TYP.getCellType());
            cell.setCellStyle(cellStyleManager.getStyle(DATA));
            String value = auftrag.objektTyp;
            if (value == null) {
                cell.setCellValue("");
            } else {
                cell.setCellValue(value);
            }
        } else if (columnIndex == WUNSCHDATUM_EM.getPosition()) {
            Cell cell = row.createCell(columnIndex, WUNSCHDATUM_KB.getCellType());
            cell.setCellStyle(cellStyleManager.getStyle(DATE));
            Date value = auftrag.wunschdatumEM;
            if (value == null) {
                cell.setCellValue("");
            } else {
                cell.setCellValue(value);
            }
        } else if (columnIndex == PLANUNGSTATUS_EM.getPosition()) {
            Cell cell = row.createCell(columnIndex, PLANUNGSTATUS_EM.getCellType());
            cell.setCellStyle(cellStyleManager.getStyle(DATA));
            String value = auftrag.planungsstatusEM;
            if (value == null) {
                cell.setCellValue("");
            } else {
                cell.setCellValue(value);
            }
        } else if (columnIndex == BELEG_EM.getPosition()) {
            Cell cell = row.createCell(columnIndex, BELEG_EM.getCellType());
            cell.setCellStyle(cellStyleManager.getStyle(DATA));
            Integer value = auftrag.auftragEM;
            if (value == null) {
                cell.setCellValue("");
            } else {
                cell.setCellValue(value.intValue());
            }
        } else if (columnIndex == KM_MONT.getPosition()) {
            Cell cell = row.createCell(columnIndex, KM_MONT.getCellType());
            cell.setCellStyle(cellStyleManager.getStyle(DATA));
            String value = auftrag.monteurKB;
            if (StringUtils.isEmpty(value) || "000".equals(value) ) {
                cell.setCellValue("");
            } else {
                cell.setCellValue(value);
            }
        } else if (columnIndex == EM_MONT.getPosition()) {
            Cell cell = row.createCell(columnIndex, EM_MONT.getCellType());
            cell.setCellStyle(cellStyleManager.getStyle(DATA));
            String value = String.join(" ", auftrag.emFormulare.stream().map(e -> e.monteurCodeSoft).collect(Collectors.toSet()));
            if (value == null) {
                cell.setCellValue("");
            } else {
                cell.setCellValue(value);
            }
        } else if (columnIndex == ANZAHL_MONT.getPosition()) {
            Cell cell = row.createCell(columnIndex, ANZAHL_MONT.getCellType());
            cell.setCellStyle(cellStyleManager.getStyle(DATA));
            Set<String> monteure = auftrag.emFormulare.stream().map(e -> e.monteurCodeSoft).collect(Collectors.toSet());

            if (!StringUtils.isEmpty(auftrag.monteurKB) && !"000".equals(auftrag.monteurKB)) {
                monteure.add(auftrag.monteurKB);
            }
            cell.setCellValue(monteure.size());
        } else if (columnIndex == ANZAHL_EM.getPosition()) {
            Cell cell = row.createCell(columnIndex, ANZAHL_EM.getCellType());
            cell.setCellStyle(cellStyleManager.getStyle(DATA));
            int value = auftrag.emFormulare.size();
            cell.setCellValue(value);
        } else if (columnIndex == BEZUGDATUM.getPosition()) {
            Cell cell = row.createCell(columnIndex, BEZUGDATUM.getCellType());
            cell.setCellStyle(cellStyleManager.getStyle(DATE));
            Date value = auftrag.bezugsDatum;
            if (value == null) {
                cell.setCellValue("");
            } else {
                cell.setCellValue(value);
            }
        } else if (columnIndex == ZEIT_EM.getPosition()) {
            Cell cell = row.createCell(columnIndex, ZEIT_EM.getCellType());
            cell.setCellStyle(cellStyleManager.getStyle(DATA));
            Integer value = calculateTimeDifferenceInDays(auftrag.wunschdatum, auftrag.wunschdatumEM);
            if (value != null) {
                cell.setCellValue(value);
            }
        } else if (columnIndex == GU.getPosition()) {
            Cell cell = row.createCell(columnIndex, GU.getCellType());
            cell.setCellStyle(cellStyleManager.getStyle(DATA));
            String value = null;
            if (LWGranit2Code.EIGENTUM.getLongText().equals(auftrag.objektTyp) || LWGranit2Code.MIET.getLongText().equals(auftrag.objektTyp)) {
                value = auftrag.gu;
            } else {
                if (auftrag.guEKRolle != null & "004".equals(auftrag.guEKRolle)) { // Nur setzen, wenn die Rolle GU = "004" ist (Appcode Adressart)
                    value = auftrag.guEK;
                }
            }
            if (value == null) {
                cell.setCellValue("");
            } else {
                cell.setCellValue(value);
            }
        } else if (columnIndex == BAUART.getPosition()) {
            Cell cell = row.createCell(columnIndex, BAUART.getCellType());
            cell.setCellStyle(cellStyleManager.getStyle(DATA));
            String value = auftrag.bauart;
            if (value == null) {
                cell.setCellValue("");
            } else {
                cell.setCellValue(value);
            }
        } else if (columnIndex == DISPONENT_EM.getPosition()) {
            Cell cell = row.createCell(columnIndex, DISPONENT_EM.getCellType());
            cell.setCellStyle(cellStyleManager.getStyle(DATA));
            String value = auftrag.disponentEM;
            if (value == null) {
                cell.setCellValue("");
            } else {
                cell.setCellValue(value);
            }
        } else if (columnIndex == DISPONENT_KM.getPosition()) {
            Cell cell = row.createCell(columnIndex, DISPONENT_KM.getCellType());
            cell.setCellStyle(cellStyleManager.getStyle(DATA));
            String value = auftrag.disponentKB;
            if (value == null) {
                cell.setCellValue("");
            } else {
                cell.setCellValue(value);
            }
        } else if (columnIndex == INFO_MASSAUFNAHME.getPosition()) {
            Cell cell = row.createCell(columnIndex, INFO_MASSAUFNAHME.getCellType());
            cell.setCellStyle(cellStyleManager.getStyle(DATA));
            String value = auftrag.infoMassaufnahme;
            if (value == null) {
                cell.setCellValue("");
            } else {
                cell.setCellValue(value);
            }
        } else if (columnIndex == DATUM_ABNAHME.getPosition()) {
            Cell cell = row.createCell(columnIndex, DATUM_ABNAHME.getCellType());
            cell.setCellStyle(cellStyleManager.getStyle(DATE));
            Date value = auftrag.abnahmen.isEmpty() ? null : Collections.max(auftrag.abnahmen);
            if (value == null) {
                cell.setCellValue("");
            } else {

                cell.setCellValue(value);
            }
        } else if (columnIndex == OFFENE_MONTAGEABSCHLUSS.getPosition()) {
            Cell cell = row.createCell(columnIndex, OFFENE_MONTAGEABSCHLUSS.getCellType());
            cell.setCellStyle(cellStyleManager.getStyle(DATA));
            int value = auftrag.offenerMontagescheine.size();
            if (value == 0) {
                cell.setCellValue("");
            } else {
                cell.setCellValue("OFFEN");
            }
        } else if (columnIndex == KM_MONT_NAME.getPosition()) {
            Cell cell = row.createCell(columnIndex, KM_MONT_NAME.getCellType());
            cell.setCellStyle(cellStyleManager.getStyle(DATA));
            String value = auftrag.monteurKBName;
            if (StringUtils.isEmpty(value) || "000".equals(value)) {
                cell.setCellValue("");
            } else {
                cell.setCellValue(value);
            }
        } else if (columnIndex == OFFENE_LIEFERSCHEINE.getPosition()) {
            Cell cell = row.createCell(columnIndex, OFFENE_LIEFERSCHEINE.getCellType());
            cell.setCellStyle(cellStyleManager.getStyle(DATA));
            int value = auftrag.offenerLieferscheine.size();
            if (value == 0) {
                cell.setCellValue("");
            } else {
                cell.setCellValue("OFFEN");
            }
        } else {
            //do nothing
        }
    }

    /**
     * @param wunschDatumKM
     * @param wunschDatumEM
     * @return
     */
    private static Integer calculateTimeDifferenceInDays(Date wunschDatumKM, Date wunschDatumEM) {

        if (!TimestampUtil.isNull(wunschDatumKM) && !TimestampUtil.isNull(wunschDatumEM)) {
            return TimestampUtil.numberOfCalendarDaysBetween(wunschDatumKM.getTime(), wunschDatumEM.getTime());
        } else {
            return null;
        }

    }
}




