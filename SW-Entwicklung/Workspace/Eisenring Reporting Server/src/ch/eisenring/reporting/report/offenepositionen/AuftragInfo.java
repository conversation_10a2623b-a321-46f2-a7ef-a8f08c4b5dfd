package ch.eisenring.reporting.report.offenepositionen;

import ch.eisenring.logiware.LWAuftragKey;
import ch.eisenring.logiware.LWProjektKey;
import ch.eisenring.logiware.code.hard.LWPlanungStatusCode;
import ch.eisenring.logiware.code.pseudo.AbwicklungsartCode;
import ch.eisenring.logiware.code.soft.LWGranit2Code;
import ch.eisenring.logiware.code.soft.LWMonteurSirCode;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;

public class AuftragInfo {
    public LWAuftragKey auftragKey;
    public LWProjektKey basis;
    public LWProjektKey projekt;
    public String objektBezeichnung;
    public LWGranit2Code objektArt;
    public String kanton;
    public Integer auftragNr;

    public AbwicklungsartCode awa;
    public String tpl;
    public String kpl;
    public String bemusterer;
    public String besteller;
    public String disponentNameAuftragEM;
    public String disponentNameAuftragKB;
    public String monteurAuftrag;
    public String firmaUK;
    public Date wunschdatumAuftrag;
    public Date bezugsDatumAuftrag;
    public Date kuechenEndeAuftrag;
    public Date wunschDatumEM;
    public Date kuechenEndeEM;
    public Date erfassungsDatumEM;
    public LWPlanungStatusCode planungsStatusEM;
    public Integer anzahlEM;
    public Date statusRapport;

    public List<PositionsInfo> positionsInfos;
    public LWProjektKey kommision;
    public String projektBezeichnung;
    public String plz;
    public String strasse;
    public Date datumStatusrapport;
    public LWMonteurSirCode monteurCodeAuftragEM;
    public String monteurNameAuftragEM;
    public String monteurNameAuftragKB;
    public LWMonteurSirCode monteurCodeAuftragKB;
    public String monteurTeamAuftragKB;

    public AuftragInfo(LWAuftragKey auftragKey) {
        this.auftragKey = auftragKey;
        positionsInfos = new ArrayList<>();
    }


}
