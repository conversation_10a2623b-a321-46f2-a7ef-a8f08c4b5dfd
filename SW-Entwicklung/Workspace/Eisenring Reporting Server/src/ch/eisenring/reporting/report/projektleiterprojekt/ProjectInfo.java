package ch.eisenring.reporting.report.projektleiterprojekt;

import ch.eisenring.core.codetype.AbstractCode;
import ch.eisenring.core.collections.List;
import ch.eisenring.core.collections.Map;
import ch.eisenring.core.collections.Set;
import ch.eisenring.core.collections.impl.ArrayList;
import ch.eisenring.core.collections.impl.ArraySet;
import ch.eisenring.core.collections.impl.HashMap;
import ch.eisenring.core.collections.impl.HashSet;
import ch.eisenring.core.datatypes.date.TimestampUtil;
import ch.eisenring.core.datatypes.strings.StringMaker;
import ch.eisenring.core.datatypes.strings.Strings;
import ch.eisenring.core.datatypes.strings.ToString;
import ch.eisenring.core.sort.Comparator;
import ch.eisenring.logiware.LWProjektKey;
import ch.eisenring.logiware.code.soft.*;

import java.time.LocalDate;
import java.time.ZoneId;
import java.util.Collections;
import java.util.Date;

public class ProjectInfo implements Comparable<ProjectInfo> {
    public final LWProjektKey key;
    public final String bezeichnung;
    public final String anzahlKuechen;
    public final boolean isObjekt;
    public boolean objektUebergabe;
    public boolean werkvertrag;
    public boolean kroki;
    public int anzahlOffen;
    public int anzahlAbgeschlossen;
    public LWMonteurSirCode bauleitenderMonteur;
    public String commentOB;
    public String kanton;
    public String textGU;
    public String nameBaufuehrer;
    public String emailBaufuehrer;
    public String phoneBaufuehrer;
    public String mobileBaufuehrer;
    public Set<LWVerkaueferCode> verkaeufer = new HashSet<>(2);
    public Set<LWKundenberaterCode> kundenberater = new HashSet<>(2);
    public LWObjektBetreuerCode tpl;
    public LWObjektBetreuerCode kpl;
    public List<LWGranit1Code> g1codes = new ArrayList<>(3);
    public List<LWGranit2Code> g2codes = new ArrayList<>(3);
    public List<LWGranitCCode> gCcodes = new ArrayList<>(3);
    public long first133 = TimestampUtil.NULL_TIMESTAMP;
    public boolean vip;

    public final Map<LocalDate, KWInfo> kwMap = new HashMap<>();

    public ProjectInfo(final String basis,
                       final GSECode gse,
                       final String bezeichnung,
                       final String anzahl,
                       final boolean isObjekt) throws IllegalArgumentException {
        this.key = LWProjektKey.get(LWProjektKey.getSignificantProjektnummer(basis), gse);
        this.bezeichnung = Strings.toSingleLine(bezeichnung);
        this.anzahlKuechen = Strings.clean(anzahl);
        this.isObjekt = isObjekt;
    }

    public void addGranit1Code(final LWGranit1Code g1code) {
        if (g1code == null || g1codes.contains(g1code))
            return;
        g1codes.add(g1code);
    }

    public void addObjekttyp(final LWGranit2Code g2code) {
        if (g2code == null || g2codes.contains(g2code))
            return;
        g2codes.add(g2code);
    }

    public void addGranitCCode(final LWGranitCCode gCcode) {
        if (gCcode == null || gCcodes.contains(gCcode))
            return;
        gCcodes.add(gCcode);
    }

    public void addKueche(final KuechenInfo kuechenInfo) {
        final long monday = TimestampUtil.findMonday(kuechenInfo.wunschDatum, 0);
        final LocalDate mondayDate = TimestampUtil.toDate(monday).toInstant()
                                  .atZone(ZoneId.systemDefault())
                                  .toLocalDate();
        KWInfo kwInfo = kwMap.get(mondayDate);
        if (kwInfo == null) {
            kwInfo = new KWInfo(monday);
            kwMap.put(mondayDate, kwInfo);
        }
        kwInfo.anzahl++;
        if (kuechenInfo.abgenommen)
            kwInfo.anzahlAbgenommen++;
        if (kuechenInfo.gemessen)
            kwInfo.anzahlGemessen++;
    }

    public String getOB() {
        return tpl.getShortText();
    }

    public String getKPL() {
        return kpl.getShortText();
    }


    public String getVerkaeufer() {
        final StringMaker b = StringMaker.obtain();
        for (final LWVerkaueferCode verkaeufer : this.verkaeufer) {
            if (b.length() > 0)
                b.append(" ");
            b.append(Strings.toString(AbstractCode.getKey(verkaeufer, (Object) null)));
        }
        return b.release();
    }

    public String getKundenberater() {
        final StringMaker b = StringMaker.obtain();
        for (final LWKundenberaterCode kundenberater : this.kundenberater) {
            if (b.length() > 0)
                b.append(" ");
            b.append(AbstractCode.getShortText(kundenberater, "???"));
        }
        return b.release();
    }

    static String codeList(final java.util.Collection<? extends AbstractCode> codes,
                           final Comparator<AbstractCode> order,
                           final ToString<AbstractCode> converter,
                           final String separator) {
        if (codes == null || codes.isEmpty())
            return "";
        final List<AbstractCode> list = new ArrayList<>(codes);
        Collections.sort(list, order);
        final StringMaker b = StringMaker.obtain();
        int i = -1;
        for (final AbstractCode code : list) {
            if (++i > 0)
                b.append(separator);
            b.append(converter.toString(code));
        }
        return b.release();
    }

    public String getGranit1Codes() {
        return codeList(g1codes, AbstractCode.Order.ShortText, AbstractCode.Conversion.ShortText, "");
    }

    public String getObjekttypen() {
        return codeList(g2codes, AbstractCode.Order.ShortText, AbstractCode.Conversion.ShortText, "");
    }

    public String getGranitCCodes() {
        return codeList(gCcodes, AbstractCode.Order.LongText, AbstractCode.Conversion.LongText, "");
    }

    public String getKey() {
        return key.getProjektnummer();
    }

    @Override
    public int compareTo(final ProjectInfo o) {
        return key.compareTo(o.key);
    }

    @Override
    public int hashCode() {
        return key.hashCode();
    }

    @Override
    public boolean equals(final Object o) {
        return o instanceof ProjectInfo && ((ProjectInfo) o).key.equals(key);
    }
}
