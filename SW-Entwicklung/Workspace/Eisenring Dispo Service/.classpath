<?xml version="1.0" encoding="UTF-8"?>
<classpath>
	<classpathentry kind="src" path="src"/>
	<classpathentry combineaccessrules="false" kind="src" path="/Eisenring Core"/>
	<classpathentry combineaccessrules="false" kind="src" path="/Eisenring App Shared"/>
	<classpathentry combineaccessrules="false" kind="src" path="/Eisenring Logiware Shared"/>
	<classpathentry combineaccessrules="false" kind="src" path="/Eisenring Model Shared"/>
	<classpathentry kind="con" path="org.eclipse.jdt.launching.JRE_CONTAINER"/>
	<classpathentry kind="con" path="org.eclipse.jdt.USER_LIBRARY/tutti"/>
	<classpathentry kind="output" path="bin"/>
</classpath>
