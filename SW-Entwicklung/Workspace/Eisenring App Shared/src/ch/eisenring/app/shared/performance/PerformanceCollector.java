package ch.eisenring.app.shared.performance;

import java.util.concurrent.atomic.AtomicReference;

import ch.eisenring.app.shared.Configurable;
import ch.eisenring.app.shared.SoftwareCore;
import ch.eisenring.core.application.Configuration;
import ch.eisenring.core.application.ConfigurationException;
import ch.eisenring.core.collections.Collection;
import ch.eisenring.core.datatypes.date.TimeOfDay;
import ch.eisenring.core.datatypes.strings.Strings;
import ch.eisenring.network.MasterPacketDispatcher;
import ch.eisenring.network.NetworkPerformanceListener;

public final class PerformanceCollector implements NetworkPerformanceListener, Configurable {

	private final AtomicReference<RawData> dataPoints = new AtomicReference<>();

	private final SoftwareCore<?> core;
	private final PerformanceWriter writer;

	volatile boolean enabled;
	
	private String logDir;
	long logInterval;

	TimeOfDay[] logTimes; 
	
	public PerformanceCollector(final SoftwareCore<?> core) {
		this.core = core;
		this.writer = new PerformanceWriter(core);
	}

	public String getLogDir() {
		return logDir;
	}

	/**
	 * Returns true if performance monitoring is enabled.
	 */
	public boolean isEnabled() {
		return enabled;
	}
	
	/**
	 * Enabled/Disables this collector
	 */
	public void setEnabled(final boolean enabled) {
		this.enabled = enabled;
		if (enabled) {
			MasterPacketDispatcher.INSTANCE.setPerformanceListener(this);
			core.addTickListener(writer);
		} else {
			MasterPacketDispatcher.INSTANCE.setPerformanceListener(null);
			core.removeTickListener(writer);
		}
	}
	
	/**
	 * Adds a data point 
	 */
	@Override
	public void addDataPoint(final Object type, final long value) {
		if (!isEnabled())
			return;
		addDataPoint(new RawData(type, value));
	}
	
	void addDataPoint(final RawData point) {
		final AtomicReference<RawData> dataPoints = this.dataPoints;
		while (true) {
			final RawData expect = dataPoints.get();
			point.next = expect;
			if (dataPoints.compareAndSet(expect, point))
				return;
		}
	}

	/**
	 * Gets all data points recorded and resets the data point list
	 * to empty.
	 */
	public RawData getAndClearDataPoints() {
		final AtomicReference<RawData> dataPoints = this.dataPoints;
		while (true) {
			RawData expect = dataPoints.get();
			if (dataPoints.compareAndSet(expect, null))
				return expect;
		}
	}

	// --------------------------------------------------------------
	// ---
	// --- Configures the collector
	// ---
	// --------------------------------------------------------------
	@Override
	public void getConfigurationFileNames(final Collection<String> fileNames) {
		// nothing
	}

	@Override
	public void configure(final Configuration allConfig) throws ConfigurationException {
		setEnabled(false);
		final Configuration cfg = allConfig.subConfiguration("PerformanceMonitoring:");

		final boolean enabled = cfg.getBoolean("Enabled", false);
		this.logDir = cfg.getString("LogDir", null);
		this.logInterval = cfg.getLong("LogInterval", 60, 86400, -1) * 1000;
		{
			TimeOfDay[] times;
			try {
				final String list = cfg.getString("LogTimes", null);
				times = TimeOfDay.parseList(list, ';');
			} catch (final Exception e) {
				times = null;
			}
			this.logTimes = times;
		}
		// ensure configuration is valid before actually enabling
		boolean reallyEnabled = enabled;
		reallyEnabled &= !Strings.isEmpty(logDir);
		reallyEnabled &= logInterval > 0;
		setEnabled(reallyEnabled);
	}

}

	