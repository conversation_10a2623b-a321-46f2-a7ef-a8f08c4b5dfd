package ch.eisenring.presento;

import ch.eisenring.core.collections.Set;
import ch.eisenring.model.engine.jdbc.DatabaseSpecifier;

/**
 * Constants for PRESENTO table and columns
 */
public interface PresentoConstants {

	public final static String TABLE_EMPLOYEES	= "PERSSTAM";
	public final static String TABLE_ENTRY		= "EINAUS";
	public final static String TABLE_GROUPS		= "GRUPPEN";
	public final static String TABLE_GROUPMEMBER= "GRUPPENMITGLIEDER";
	public final static String TABLE_ABSENCES   = "STEMP";
	
	public final static String COLUMN_EMP_PK = "PERSNR";
	public final static String COLUMN_EMP_PERSONALNR = "PERSONALNR";
	public final static String COLUMN_EMP_FIRSTNAME = "VORNAME";
	public final static String COLUMN_EMP_LASTNAME = "NAME";
	public final static String COLUMN_EMP_SHORTNAME = "KURZZEICH";
	public final static String COLUMN_EMP_BIRTHDATE = "GEBDAT";
	public final static String COLUMN_EMP_MOBILE = "MOBILE";

	public final static String COLUMN_ENT_FK = "PERSNR";
	public final static String COLUMN_ENT_ENTRYDATE = "EINTRITT";
	public final static String COLUMN_ENT_EXITDATE = "AUSTRITT";

	public final static String COLUMN_ABS_FK = "PERSNR";
	public final static String COLUMN_ABS_DATE = "DATUM";
	public final static String COLUMN_ABS_CODE = "STMCODE";
	
	public final static String COLUMN_GRP_PK = "GRPID";
	public final static String COLUMN_GRP_NUMBER = "NUMMER";

	public final static String COLUMN_GRM_GRP_FK = "GRPID";
	public final static String COLUMN_GRM_EMP_FK = "PERSNR";

	// hack für "MONT"
	public final static long MONT_PRESENTO_ID = 262L;
	public final static String MONT_SHORTNAME = "MONT";
	public final static long PRESENTO_NULL_ID = 0L;

	public final static DatabaseSpecifier PRESENTO_DATABASE = DatabaseSpecifier.get("Presento");

	/**
	 * Set of Id's defined by presento itself. These will not be loaded by the cache
	 */
	public final static Set<Long> PRESENTO_INTERNAL_IDS = Set.asReadonlySet(
			Long.valueOf(0),
			Long.valueOf(-1),
			Long.valueOf(-2),
			(Long) null
		);

}
