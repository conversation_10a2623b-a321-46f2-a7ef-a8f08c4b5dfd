DROP TABLE DMSPermission;
DROP TABLE DMSAuditTrail;
DROP TABLE DMSProperty;
DROP TABLE DMSObjectLock;
DROP TABLE DMSBinary;
DROP TABLE DMSObject;
DROP TABLE DMSBlob;

-- Permissions ------------------------------------------------------
CREATE TABLE DMSPermission (
  rowId            BIGINT NOT NULL,
  privateFlag      INT NOT NULL DEFAULT(0),
  name             VARCHAR(30) NOT NULL,
  permissions      VARCHAR(MAX)
);

ALTER TABLE DMSPermission ADD PRIMARY KEY (rowId);
-- permission name must be unique
CREATE UNIQUE INDEX IDX_Permission_Name ON DMSPermission(name);



-- Object -----------------------------------------------------------
CREATE TABLE DMSObject (
  rowId            BIGINT NOT NULL,
  Parent_rowId     BIGINT,
  Permission_rowId BIGINT,
  Typecode         SMALLINT NOT NULL,
  Statuscode       SMALLINT NOT NULL,
  Dirtycode        SMALLINT NOT NULL,
  Iconcode         SMALLINT NOT NULL,
  tru_typeclass    SMALLINT,
  Bytesize         BIGINT NOT NULL,
  LastChanged      DATETIME NOT NULL,
  FileExtension    VARCHAR(16),
  Objectname       VARCHAR(250) NOT NULL,
  Participants     VARCHAR(250),
  Keywords         VARCHAR(2004),
  Thumbnail        VARBINARY(MAX),
  Searchwords      VARCHAR(MAX)
);

ALTER TABLE DMSObject ADD PRIMARY KEY (rowId);
ALTER TABLE DMSObject ADD FOREIGN KEY (Parent_rowId) REFERENCES DMSObject(rowId);
ALTER TABLE DMSObject ADD FOREIGN KEY (Permission_rowId) REFERENCES DMSPermission(rowId);

-- object name must be unique in directory
CREATE UNIQUE INDEX IDX_ObjectParentRowIdObjectname ON DMSObject(Parent_rowId, Objectname);
-- fast lookup for parent/child relationship
CREATE INDEX IDX_ObjectParentRowId ON DMSObject(Parent_rowId) INCLUDE (Typecode, Statuscode);
-- fast searches by object name with type/status constraints
CREATE INDEX IDX_Objectname ON DMSObject(rowId, Objectname) INCLUDE (Typecode, Statuscode);
-- accelerates full text searches with metadata constraints
CREATE INDEX IDX_LastChanged ON DMSObject(rowId, LastChanged) INCLUDE (Bytesize);


-- Full text index -------------------------------------------------
CREATE TABLE DMSSearchIndex (
  Object_rowId     BIGINT NOT NULL,
  Searchwords      VARCHAR(MAX)
);

ALTER TABLE DMSSearchIndex ADD PRIMARY KEY (Object_rowId);



-- Object Lock ------------------------------------------------------
CREATE TABLE DMSObjectLock (
  Object_rowId      BIGINT NOT NULL,
  LockTime          DATETIME NOT NULL,
  LockUser          VARCHAR(60) NOT NULL,
  LockHost          VARCHAR(256),
  LockPath          VARCHAR(1024)
);

ALTER TABLE DMSObjectLock ADD PRIMARY KEY (Object_rowId);
CREATE INDEX IDX_DMSObjectLock_ObjectRowId ON DMSObjectLock(Object_rowId);



-- Binary data ------------------------------------------------------
CREATE TABLE DMSBinary (
  rowId            BIGINT NOT NULL,
  Object_rowId     BIGINT NOT NULL,
  Bytesize         BIGINT NOT NULL,
  Rawsize          BIGINT NOT NULL,
  Statuscode       SMALLINT NOT NULL,
  MadeOn           DATETIME NOT NULL,
  FileExtension    VARCHAR(16),
  Binaryhash       VARCHAR(80),
  Plaintext        VARCHAR(MAX), 
  Binarydata       VARBINARY(MAX),                    
  Plainversion     INT NOT NULL DEFAULT 0,
  DCMCode          SMALLINT NOT NULL DEFAULT 0
);

ALTER TABLE DMSBinary ADD PRIMARY KEY (rowId);
CREATE INDEX IDX_DMSBinary_ObjectRowId ON DMSBinary(Object_rowId);
-- hash can be used to find potential version duplicates quickly
-- (currently not needed, since we do not check for dupes)
-- CREATE INDEX IDX_BinaryBinaryhash ON DMSBinary(Binaryhash);


-- Audit trail ------------------------------------------------------
CREATE TABLE DMSAuditTrail (
  rowId             BIGINT IDENTITY,
  Object_rowId      BIGINT NOT NULL,
  Binary_rowId      BIGINT,
  Auditcode         SMALLINT NOT NULL,
  MadeOn            DATETIME NOT NULL,
  MadeBy            VARCHAR(60) NOT NULL,
  ValueOld          VARCHAR(250),
  ValueNew          VARCHAR(250)
);

ALTER TABLE DMSAuditTrail ADD PRIMARY KEY (rowId);
-- accelerates audit retrieval for object id
CREATE INDEX IDX_AuditTrailObjectRowId ON DMSAuditTrail(Object_RowId);
-- accelerates deletes
CREATE INDEX IDX_AuditTrailBinaryRowId ON DMSAuditTrail(Binary_RowId);



-- Properties -------------------------------------------------------
CREATE TABLE DMSProperty (
  rowId             BIGINT NOT NULL,
  Object_rowId      BIGINT NOT NULL,
  Propertycode      SMALLINT NOT NULL,
  Propertyvalue     VARCHAR(250)
);

ALTER TABLE DMSProperty ADD PRIMARY KEY (rowId);
ALTER TABLE DMSProperty ADD FOREIGN KEY (Object_rowId) REFERENCES DMSObject(rowId);
-- only one property of each type per object
CREATE UNIQUE INDEX IDX_Propertycode ON DMSProperty (Object_rowId, Propertycode);
-- accelerate searches by property value 
CREATE INDEX IDX_Propertyvalue ON DMSProperty (Propertyvalue) INCLUDE (Object_rowId, Propertycode);



-- Blob Storage -----------------------------------------------------
CREATE TABLE DMSBlob (
  rowId             BIGINT NOT NULL,
  blobKeyType       INT NOT NULL,
  blobKeyValue      VARCHAR(60) NOT NULL,
  blobData          VARBINARY(MAX)
);

ALTER TABLE DMSBlob ADD PRIMARY KEY (rowId);
CREATE UNIQUE INDEX IDX_DMSBlob ON DMSBlob(blobKeyValue, blobKeyType);



-- Constraints ------------------------------------------------------

ALTER TABLE DMSSearchIndex ADD FOREIGN KEY (Object_rowId) REFERENCES DMSObject(rowId);
ALTER TABLE DMSObjectLock ADD FOREIGN KEY (Object_rowId) REFERENCES DMSObject(rowId);
ALTER TABLE DMSBinary ADD FOREIGN KEY (Object_rowId) REFERENCES DMSObject(rowId);
ALTER TABLE DMSObjectLock ADD FOREIGN KEY (Object_rowId) REFERENCES DMSObject(rowId);
ALTER TABLE DMSAuditTrail ADD FOREIGN KEY (Object_rowId) REFERENCES DMSObject(rowId);
ALTER TABLE DMSAuditTrail ADD FOREIGN KEY (Binary_rowId) REFERENCES DMSBinary(rowId);

GRANT ALL ON DMSPermission TO PUBLIC;
GRANT ALL ON DMSObject TO PUBLIC;
GRANT ALL ON DMSObjectLock TO PUBLIC;
GRANT ALL ON DMSBinary TO PUBLIC;
GRANT ALL ON DMSAuditTrail TO PUBLIC;
GRANT ALL ON DMSProperty TO PUBLIC;
GRANT ALL ON DMSBlob TO PUBLIC;
GRANT ALL ON DMSSearchIndex TO PUBLIC;

-- Cascading Delete triggers ----------------------------------------
CREATE TRIGGER DMSObject_Delete ON DMSObject INSTEAD OF DELETE
AS
  IF @@ROWCOUNT = 0 RETURN
  SET NOCOUNT ON  
  DELETE FROM DMSAuditTrail WHERE Object_rowId IN (SELECT rowId FROM deleted)
  DELETE FROM DMSProperty WHERE Object_rowId IN (SELECT rowId FROM deleted)
  DELETE FROM DMSBinary WHERE Object_rowId IN (SELECT rowId FROM deleted)
  DELETE FROM DMSObjectLock WHERE Object_rowId IN (SELECT rowId FROM deleted)
  DELETE FROM DMSSearchIndex WHERE Object_rowId IN (SELECT rowId FROM deleted)
  DELETE FROM DMSObject WHERE rowId IN (SELECT rowId FROM deleted);

-- Insert default data ----------------------------------------------

-- Root node
--INSERT INTO DMSObject VALUES (
--  111111, NULL, NULL, 1, 1, 0, 0, 0, GETDATE(), NULL, NULL, NULL,
--  'Dokumente', NULL, NULL, NULL, NULL); 

