package ch.eisenring.dms.server.network;

import java.sql.SQLException;
import java.util.Iterator;

import ch.eisenring.core.codetable.ErrorMessage;
import ch.eisenring.core.datatypes.strings.Strings;
import ch.eisenring.dms.server.DMSServer;
import ch.eisenring.dms.server.audit.AuditTrailBatch;
import ch.eisenring.dms.server.dao.DMSContextSink;
import ch.eisenring.dms.shared.codetables.DMSAuditCode;
import ch.eisenring.dms.shared.model.record.DMSObjectModel;
import ch.eisenring.dms.shared.network.PacketSetParticipantsReply;
import ch.eisenring.dms.shared.network.PacketSetParticipantsRequest;
import ch.eisenring.dms.shared.network.cmd.ObjectMessageConstants;
import ch.eisenring.dms.shared.network.cmd.ObjectMessageSet;
import ch.eisenring.model.Model;
import ch.eisenring.model.TransactionContext;
import ch.eisenring.network.PacketSink;
import ch.eisenring.network.packet.AbstractPacket;

public final class SetParticipantsRequestHandler extends AbstractDMSPacketHandler {

	private SetParticipantsRequestHandler(final DMSServer server) {
		super(server, PacketSetParticipantsRequest.class);
	}

	@Override
	public void handle(final AbstractPacket abstractPacket, final PacketSink sink) {
		final PacketSetParticipantsRequest request = (PacketSetParticipantsRequest) abstractPacket;
		final ObjectMessageSet changeMessages = new ObjectMessageSet();
		// rename & reload object
		final TransactionContext context = new TransactionContext();
		final DMSContextSink contextSink = new DMSContextSink(server) {
			@Override
			public void storeImpl(final Iterator<Model> modelItr, final TransactionContext context) throws SQLException {
				final Long objectRowId = request.getRowId();
				long rowCount;
				
				// load the existing object
				DMSObjectModel object = loadObject(context, objectRowId, true, false);
				final String valueOld = object.getParticipants();
				final String valueNew = request.getParticipants();
				// update the participants
				if (valueOld == null) {
					rowCount = update(
							"UPDATE ${DMSObject} SET ${Participants} = ? WHERE ${PK} = ? AND ${Participants} IS NULL",
							DMSObjectModel.METACLASS,
							valueNew, objectRowId);
				} else {
					rowCount = update(
							"UPDATE ${DMSObject} SET ${Participants} = ? WHERE ${PK} = ? AND ${Participants} = ?",
							DMSObjectModel.METACLASS,
							valueNew, objectRowId, valueOld);
				}
				if (rowCount != 1) {
					throw new SQLException(Strings.concat("UPDATE returned unexpected affected row count: ", rowCount, " (expected: 1)"));
				}
				final AuditTrailBatch auditBatch = new AuditTrailBatch();
				auditBatch.add(request.getRowId(), null, DMSAuditCode.PARTICIPANTS,
						request.getUser(), valueOld, valueNew);
				auditBatch.insertBatch(this);
				changeMessages.addFlags(objectRowId, ObjectMessageConstants.ATTRIBUTES);
				changeMessages.addFlags(object.getParentRowId(), ObjectMessageConstants.CHILDREN);
			}
		};
		try {
			context.store(contextSink);
			final PacketSetParticipantsReply reply = PacketSetParticipantsReply.create(request, changeMessages);
			sink.sendPacket(reply, false);
			broadcastMessages(changeMessages);
		} catch (final Exception e) {
			final PacketSetParticipantsReply reply = PacketSetParticipantsReply.create(request, new ErrorMessage(e));
			sink.sendPacket(reply, false);
		}
	}

}
