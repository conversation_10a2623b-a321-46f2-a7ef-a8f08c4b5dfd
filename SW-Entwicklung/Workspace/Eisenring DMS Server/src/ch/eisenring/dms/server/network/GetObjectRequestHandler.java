package ch.eisenring.dms.server.network;

import static ch.eisenring.dms.server.dao.DMSModelMapping.LOADER_OBJECT;

import java.sql.SQLException;

import ch.eisenring.app.server.model.ContextSource;
import ch.eisenring.core.codetable.ErrorMessage;
import ch.eisenring.core.collections.List;
import ch.eisenring.core.collections.impl.ArrayList;
import ch.eisenring.core.datatypes.strings.StringMaker;
import ch.eisenring.core.datatypes.strings.Strings;
import ch.eisenring.dms.server.DMSServer;
import ch.eisenring.dms.service.api.DMSObject;
import ch.eisenring.dms.shared.DMSConstants;
import ch.eisenring.dms.shared.network.PacketGetObjectReply;
import ch.eisenring.dms.shared.network.PacketGetObjectRequest;
import ch.eisenring.jdbc.StatementParameters;
import ch.eisenring.model.TransactionContext;
import ch.eisenring.model.TransactionSource;
import ch.eisenring.model.engine.jdbc.CollectingRowHandler;
import ch.eisenring.model.engine.jdbc.SingleTableMapping;
import ch.eisenring.model.engine.jdbc.TableMapping;
import ch.eisenring.network.PacketSink;
import ch.eisenring.network.packet.AbstractPacket;

public final class GetObjectRequestHandler extends AbstractDMSPacketHandler {

	public GetObjectRequestHandler(final DMSServer server) {
		super(server, PacketGetObjectRequest.class);
	}

	@Override
	public void handle(final AbstractPacket abstractPacket, final PacketSink sink) {
		final PacketGetObjectRequest request = (PacketGetObjectRequest) abstractPacket;
		final PacketGetObjectReply reply = handle(server, request);
		sink.sendPacket(reply, false);
	}

	public static PacketGetObjectReply handle(final DMSServer server, final PacketGetObjectRequest request) {
		try {
			final PacketGetObjectReply reply = handleImpl(server, request);
			if (reply != null)
				return reply;
			return PacketGetObjectReply.create(request, ErrorMessage.ERROR);
		} catch (final Exception e) {
			return PacketGetObjectReply.create(request, new ErrorMessage(e));
		}
	}

	private static PacketGetObjectReply handleImpl(final DMSServer server, final PacketGetObjectRequest request) throws Exception {
		if (request.getRowId() == null)
			throw new IllegalArgumentException("Es gibt kein Objekt mit der Id NULL");
		final PacketGetObjectReply reply = PacketGetObjectReply.create(request, ErrorMessage.OK);
		final List<DMSObject> result = new ArrayList<>();
		final TransactionSource source = new ContextSource(DMSConstants.DMS_DATABASE) {
			@Override
			protected void loadImpl(final TransactionContext context) throws SQLException {
				final SingleTableMapping tableMapping = LOADER_OBJECT.getTableMapping();
				final StringMaker sql = StringMaker.obtain(512);
				final List<?> rowIds = request.getRowIdList();
				tableMapping.appendSelectSQL(sql, this, TableMapping.NO_TABLE_ALIAS);
				sql.append(" WHERE ");
				sql.append(tableMapping.getPrimaryKeyColumn());
				sql.append(" IN ");
				prepareIn(sql, rowIds);
				final StatementParameters params = new StatementParameters();
				params.addAll(rowIds);
				doQuery(sql.release(), params, new CollectingRowHandler<>(LOADER_OBJECT, result, context));
			}
		};
		final TransactionContext context = new TransactionContext();
		context.load(source);
		if (result.isEmpty())
			return PacketGetObjectReply.create(request, new ErrorMessage(Strings.concat(
					"Kein Objekt mit der Id ", request.getRowId(), " gefunden")));
		reply.addObjects(result);
		return reply;
	}

}
