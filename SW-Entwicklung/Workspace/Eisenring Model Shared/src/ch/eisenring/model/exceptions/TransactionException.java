package ch.eisenring.model.exceptions;

/**
 * Exception for meta model related causes.
 */
@SuppressWarnings("serial")
public class TransactionException extends ModelException {

	public TransactionException(final String message) {
		super(message);
	}
	
	public TransactionException(final String message, final Throwable cause) {
		super(message, cause);
	}
	
	public TransactionException(final Throwable cause) {
		super(cause);
	}

}
