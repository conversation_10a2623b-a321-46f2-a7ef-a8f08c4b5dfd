package ch.eisenring.model.factory;

import ch.eisenring.core.datatypes.primitives.Primitives;
import ch.eisenring.model.attribute.validation.RangeValidator;
import ch.eisenring.model.attribute.validation.StringLengthValidator;
import ch.eisenring.model.validation.AttributeValidator;
import ch.eisenring.model.validation.ValidationMessageType;

/**
 * Factory for commonly used AttributeValidator types.
 */
public class AttributeValidatorFactory {

	protected AttributeValidatorFactory() {
	}

	//---------------------------------------------------------------
	//---
	//--- Factory methods for common AttributeValidator types
	//---
	//---------------------------------------------------------------
	/**
	 * Creates a byte range AttributeValidator
	 */
	public static AttributeValidator byteRange(final byte minValue, final byte maxValue, final ValidationMessageType severity) {
		return new RangeValidator<Byte>(Byte.valueOf(minValue), Byte.valueOf(maxValue), severity);
	}

	/**
	 * Creates a short range AttributeValidator
	 */
	public static AttributeValidator shortRange(final short minValue, final short maxValue, final ValidationMessageType severity) {
		return new RangeValidator<Short>(Short.valueOf(minValue), Short.valueOf(maxValue), severity);
	}

	/**
	 * Creates an integer range AttributeValidator
	 */
	public static AttributeValidator intRange(final int minValue, final int maxValue, final ValidationMessageType severity) {
		return new RangeValidator<Integer>(Integer.valueOf(minValue), Integer.valueOf(maxValue), severity);
	}

	/**
	 * Creates a long range AttributeValidator
	 */
	public static AttributeValidator longRange(final long minValue, final long maxValue, final ValidationMessageType severity) {
		return new RangeValidator<Long>(Long.valueOf(minValue), Long.valueOf(maxValue), severity);
	}

	/**
	 * Creates a float range AttributeValidator
	 */
	public static AttributeValidator floatRange(final float minValue, final float maxValue, final ValidationMessageType severity) {
		return new RangeValidator<Float>(Primitives.valueOf(minValue), Primitives.valueOf(maxValue), severity);
	}

	/**
	 * Creates a double range AttributeValidator
	 */
	public static AttributeValidator doubleRange(final double minValue, final double maxValue, final ValidationMessageType severity) {
		return new RangeValidator<Double>(Primitives.valueOf(minValue), Primitives.valueOf(maxValue), severity);
	}

	/**
	 * Creates a string length AttributeValidator
	 */
	public static AttributeValidator stringLength(final int maxLength, final ValidationMessageType severity) {
		return new StringLengthValidator(maxLength, severity);
	}

}
