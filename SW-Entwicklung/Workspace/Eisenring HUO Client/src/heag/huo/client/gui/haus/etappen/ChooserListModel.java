package heag.huo.client.gui.haus.etappen;

import java.util.ArrayList;
import java.util.Collection;
import java.util.List;
import java.util.function.Predicate;
import javax.swing.AbstractListModel;

public class ChooserListModel<E> extends AbstractListModel<ListModelItem<E>> {
	private final List<ListModelItem<E>> listModelData = new ArrayList<>();
	private final Predicate<ListModelItem<E>> predicate;

	public ChooserListModel(Predicate<ListModelItem<E>> predicate) {
		this.predicate = predicate;
	}

	public boolean test(ListModelItem<E> item) {
		return predicate.test(item);
	}

	@Override
	public int getSize() {
		return listModelData.size();
	}

	@Override
	public ListModelItem<E> getElementAt(int index) {
		return listModelData.get(index);
	}

	protected synchronized void update(Collection<ListModelItem<E>> chooserModelData) {
		for (ListModelItem<E> item : chooserModelData) {
			if (test(item)) {
				int index = this.listModelData.indexOf(item);
				if (index < 0) {
					index = getSize();
					if (this.listModelData.add(item)) {
						fireIntervalAdded(this, index, index);
					}
				}
			}
		}
		for (ListModelItem<E> item : new ArrayList<>(this.listModelData)) {
			if (!test(item) || !chooserModelData.contains(item)) {
				int index = listModelData.indexOf(item);
				if (0 <= index) {
					if (this.listModelData.remove(item)) {
						fireIntervalRemoved(this, index, index);
					}
				}
			}
		}
	}

}