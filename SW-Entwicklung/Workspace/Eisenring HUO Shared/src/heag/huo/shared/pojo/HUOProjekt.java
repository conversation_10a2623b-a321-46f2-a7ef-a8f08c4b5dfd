package heag.huo.shared.pojo;

import java.io.IOException;

import ch.eisenring.core.datatypes.date.TimestampUtil;
import ch.eisenring.core.datatypes.primitives.Primitives;
import ch.eisenring.core.io.StreamReader;
import ch.eisenring.core.io.StreamWriter;
import ch.eisenring.core.io.Streamable;
import ch.eisenring.logiware.LWProjektKey;
import ch.eisenring.lw.api.LWProjektData;
import ch.eisenring.lw.model.navigable.LWProjekt;
import ch.eisenring.lw.model.navigable.LWXPAuftrag;
import ch.eisenring.lw.pojo.LWProjektDataPojo;
import ch.eisenring.lw.pojo.LWXPAuftragPojo;

/**
 * Represents Projekt in H/O Übersicht 
 */
public final class HUOProjekt implements Streamable {

	public final static String ANSICHT_BEZEICHNUNG_DEFAULT = "An<PERSON><PERSON> von <PERSON>";
	public final static String GRANITMONTAGE_TEXT_DEFAULT = "Granitmontage ungeplant";
	public final static String KUECHENMONTAGE_TEXT_DEFAULT = "Küchenmontage ungeplant";

	private final static int VERSION = 0;

	transient LWProjektData projekt;
	transient LWXPAuftragPojo lwxPAuftrag;

	// --------------------------------------------------------------
	// ---
	// --- Factory
	// ---
	// --------------------------------------------------------------
	private HUOProjekt() {
	}
	
	/**
	 * Build from LWProjekt
	 */
	public static HUOProjekt create(final LWProjekt projekt) {
		final HUOProjekt result = new HUOProjekt();
		result.projekt = LWProjektDataPojo.create(projekt);
		// Extra-Data
		final LWXPAuftragPojo exPojo = LWXPAuftragPojo.create(projekt.getProjektKey());
		final LWXPAuftrag ex = projekt.getLWXPAuftrag();
		if (ex == null) {
			exPojo.setHausBemerkungenUebersicht(null);
			exPojo.setHausAnsichtBezeichnung(ANSICHT_BEZEICHNUNG_DEFAULT);
			exPojo.setHausKuechenMontageInfo(KUECHENMONTAGE_TEXT_DEFAULT);
			exPojo.setHausGranitMontageInfo(GRANITMONTAGE_TEXT_DEFAULT);
			exPojo.setDatumBauprogramm(TimestampUtil.NULL_TIMESTAMP);
		} else {
			ex.updatePojo(exPojo);
		}
		result.lwxPAuftrag = exPojo;
		return result;
	}

	// --------------------------------------------------------------
	// ---
	// --- Getters
	// ---
	// --------------------------------------------------------------
	public LWProjektData getProjekt() {
		return projekt;
	}

	public String getProjektNummer() {
		return projekt.getProjektKey().getProjektnummer();
	}

	public LWProjektKey getProjektKey() {
		return projekt.getProjektKey();
	}

	public LWXPAuftragPojo getLWXPAuftrag() {
		return lwxPAuftrag;
	}

	// --------------------------------------------------------------
	// ---
	// --- Streamable implementation
	// ---
	// --------------------------------------------------------------
	@Override
	public void read(final StreamReader reader) throws IOException {
		final int version = reader.readInt();
		if (version != VERSION)
			throw new IOException("invalid version");
		projekt = (LWProjektData) reader.readObject();
		lwxPAuftrag = (LWXPAuftragPojo) reader.readObject();
	}

	@Override
	public void write(final StreamWriter writer) throws IOException {
		writer.writeInt(VERSION);
		writer.writeObject(projekt);
		writer.writeObject(lwxPAuftrag);
	}

	// --------------------------------------------------------------
	// ---
	// --- Object overrides
	// ---
	// --------------------------------------------------------------
	@Override
	public int hashCode() {
		return projekt.hashCode();
	}

	@Override
	public boolean equals(final Object o) {
		return o instanceof HUOProjekt && Primitives.equals(
				((HUOProjekt) o).projekt, projekt);
	}

}
