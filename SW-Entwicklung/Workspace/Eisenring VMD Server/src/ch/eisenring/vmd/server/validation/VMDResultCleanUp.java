package ch.eisenring.vmd.server.validation;

import static ch.eisenring.vmd.server.dao.VMDMapping.OM_RESULT;
import static ch.eisenring.vmd.server.dao.VMDMapping.TM_RESULT;
import static ch.eisenring.lw.LWMapping.TM_AUFTRAG;

import ch.eisenring.app.server.model.ContextSource;
import ch.eisenring.core.logging.Logger;
import ch.eisenring.jdbc.JDBCBase;
import ch.eisenring.lw.LWConstants;
import ch.eisenring.model.TransactionSource;
import ch.eisenring.model.engine.jdbc.CollectingRowHandler;
import java.io.IOException;
import java.sql.SQLException;
import java.util.Arrays;
import java.util.Iterator;
import ch.eisenring.core.collections.Collection;
import ch.eisenring.core.collections.List;
import ch.eisenring.core.collections.impl.ArrayList;
import ch.eisenring.core.collections.impl.HashSet;
import ch.eisenring.core.datatypes.strings.StringMaker;
import ch.eisenring.jdbc.JDBCResultSet;
import ch.eisenring.jdbc.RowHandler;
import ch.eisenring.jdbc.StatementParameters;
import ch.eisenring.model.Model;
import ch.eisenring.model.TransactionContext;
import ch.eisenring.model.TransactionSink;
import ch.eisenring.model.engine.jdbc.TableMapping;
import ch.eisenring.vmd.server.VMDServer;
import ch.eisenring.vmd.server.dao.VMDContextSink;
import ch.eisenring.vmd.shared.VMDConstants;
import ch.eisenring.vmd.shared.codetables.VMDResultStatusType;
import ch.eisenring.vmd.shared.model.meta.VMDRuleResultMeta;
import ch.eisenring.vmd.shared.model.pojo.VMDRuleResult;


/**
 * To clean up rule result entries in the VMD database relating to items in Logiware that do no
 * longer exist. Currently supported: AUFTRAG.
 */
public class VMDResultCleanUp {

	public final VMDServer server;

	public VMDResultCleanUp(final VMDServer server) {
		this.server = server;
	}

	/**
	 * Iterates through all OPEN entries in the rule result table and checks whether
	 * the related auftrag exists in Logiware's AUFTRAG table. If so, ignores it. If not,
	 * sets the respective entry to CLOSED.
	 */
	public void cleanUp() throws IOException {
		final List<VMDRuleResult> results = new ArrayList<>();

		final TransactionSource source = new ContextSource(VMDConstants.VMD_DATABASE) {
			@Override
			protected void loadImpl(final TransactionContext context) throws SQLException {
				final StatementParameters params = new StatementParameters();
				params.add(VMDResultStatusType.OPEN);
				params.add(VMDResultStatusType.NULL);
				final StringMaker sql = StringMaker.obtain(512);
				TM_RESULT.appendSelectSQL(sql, this, TableMapping.NO_TABLE_ALIAS);
				sql.append(" WHERE ");
				sql.append(TM_RESULT.getColumn(VMDRuleResultMeta.ATR_STATUS));
				sql.append(" IN ");
				JDBCBase.prepareIn(sql, params);
				doQuery(sql.release(), params, new CollectingRowHandler<>(OM_RESULT, results, context));
			}
		};
		final TransactionContext context = new TransactionContext();
		context.load(source);

		final HashSet<Integer> auftragIds = new HashSet<>();
		final TransactionSource source2 = new ContextSource(LWConstants.VERTRIEB) {
			@Override
			protected void loadImpl(final TransactionContext context) throws SQLException {
				final StringMaker sql = StringMaker.obtain(512);
				TM_AUFTRAG.appendSelectSQL(sql, this, TableMapping.NO_TABLE_ALIAS);
				doQuery("SELECT auftrNr FROM AUFTRAG", new RowHandler() {
					@Override
					public void handleRow(JDBCResultSet resultSet) throws SQLException {
						auftragIds.add(resultSet.getInteger(1));
					}
				});
			}
		};
		final TransactionContext context2 = new TransactionContext();
		context2.load(source2);

		final Collection<VMDRuleResult> updates = new ArrayList<>();
		for(VMDRuleResult result : results) {
			if(result.getFKValue().startsWith("A")) {
				int parsedAuftrNr = new Integer(result.getFKValue().substring(2, result.getFKValue().indexOf("|", 2)));
				if (!auftragIds.contains(parsedAuftrNr)) {
					Logger.warn("Regelverletzung verweist auf einen Auftrag, den es in Logiware nicht (mehr) gibt: " + result.getFKValue() + ", Regel " + result.getRuleId() + " -> Verletzung wird auf geschlossen gesetzt!");
					result.setStatus(VMDResultStatusType.CLOSED);
					result.setClosedOn(System.currentTimeMillis());
					result.setClosedBy(VMDConstants.VMD_ENGINE_NAME);
					updates.add(result);
				}
			}
		}

		final TransactionSink sink = new VMDContextSink(server) {
			@Override
			protected void storeImpl(Iterator<Model> modelItr, TransactionContext context)
					throws SQLException {
				OM_RESULT.update(this, updates);
			}
		};
		final TransactionContext context3 = new TransactionContext();
		context3.store(sink);
	}

}
