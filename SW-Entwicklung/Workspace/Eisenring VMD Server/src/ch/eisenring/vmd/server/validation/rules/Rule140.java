package ch.eisenring.vmd.server.validation.rules;

import ch.eisenring.dms.service.codetables.DMSDocumentType;
import ch.eisenring.logiware.LWProjektKey;
import ch.eisenring.logiware.code.soft.LWObjektStatusCode;
import ch.eisenring.lw.model.navigable.LWProjekt;
import ch.eisenring.vmd.server.VMDServer;
import ch.eisenring.vmd.server.validation.VMDRuleParameters;
import ch.eisenring.vmd.server.validation.VMDRuleValidator;
import ch.eisenring.vmd.server.validation.VMDValidationContext;
import ch.eisenring.core.collections.Set;

/**
 * Rule 140: "Objekt-Status nicht auf Auftrag erteilt bei vorhandenem Werkvertrag"
 *
 */
public class Rule140 extends VMDRuleValidator {

    public Rule140(final VMDServer server, final int ruleId) {
        super(server, ruleId);
    }

    @Override
    public void validateImpl(final VMDValidationContext validationContext) throws Exception {
        final Rule140.Parameters parameters = (Rule140.Parameters) validationContext.getParameters(this);
        final LWProjekt basis = validationContext.getValidationProjekt().getBasisProjekt();
        if (basis == null)
            return;
        final LWProjektKey basisKey = basis.getProjektKey();
        if (validationContext.getRuleAccel(basisKey) != null)
            return;
        int severity = 0;
        do {
            // Objektstatus auf "Auftrag erteilt"?
            if (parameters.basisObjektStatusCodes.contains(basis.getObjektStatus())) {
                break;
            }
            // Werkvertrag vorhanden?
            severity = hasDocument(validationContext, basisKey, parameters.documentType) ? 3 : 0;
        } while (false);
        validationContext.addResult(this, severity, basis);
        validationContext.putRuleAccel(basisKey, severity);
    }

    protected static Boolean hasDocument(final VMDValidationContext context,
                                         final LWProjektKey basisKey, final DMSDocumentType documentType) {
        try {
            final int count = context.getDMSService()
                    .countDocuments(basisKey.getProjektnummer(), documentType);
            return count > 0;
        } catch (final Exception e) {
            return Boolean.FALSE;
        }
    }

    // --------------------------------------------------------------
    // ---
    // --- Parameters
    // ---
    // --------------------------------------------------------------
    private final static Set<LWObjektStatusCode> BASIS_OBJEKTSATUSCODES_DEFAULT = Set.asReadonlySet(
            LWObjektStatusCode.C008
    );

    static class Parameters extends VMDRuleParameters {
        public final Set<LWObjektStatusCode> basisObjektStatusCodes;
        public final DMSDocumentType documentType;

        Parameters(final VMDRuleValidator rule) {
            basisObjektStatusCodes = rule.getCodeSetParameter(
                    0, LWObjektStatusCode.class, BASIS_OBJEKTSATUSCODES_DEFAULT);
            documentType = DMSDocumentType.WERKVERTRAG;
        }
    }

    @Override
    protected VMDRuleParameters getParameters() throws Exception {
        return new Rule140.Parameters(this);
    }
}
