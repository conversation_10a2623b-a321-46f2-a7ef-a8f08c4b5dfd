package ch.eisenring.vmd.server.validation.rules;

import ch.eisenring.core.collections.Set;
import ch.eisenring.core.datatypes.date.TimestampUtil;
import ch.eisenring.logiware.code.pseudo.AbwicklungsartCode;
import ch.eisenring.logiware.code.service.LogiwareService;
import ch.eisenring.logiware.code.soft.LWGranit2Code;
import ch.eisenring.lw.condition.Factory;
import ch.eisenring.lw.meta.LWProjektMeta;
import ch.eisenring.lw.model.navigable.LWAuftrag;
import ch.eisenring.lw.model.navigable.LWAuftragKopf;
import ch.eisenring.lw.model.navigable.LWObjectCache;
import ch.eisenring.lw.model.navigable.LWProjekt;
import ch.eisenring.vmd.server.VMDServer;
import ch.eisenring.vmd.server.validation.VMDRuleParameters;
import ch.eisenring.vmd.server.validation.VMDRuleValidator;
import ch.eisenring.vmd.server.validation.VMDValidationContext;

public class Rule131 extends VMDRuleValidator {

    public Rule131(final VMDServer server, final int ruleId) {
        super(server, ruleId);
    }

    @Override
    public void validateImpl(final VMDValidationContext validationContext) throws Exception {
        final Rule131.Parameters parameters = (Rule131.Parameters) validationContext.getParameters(this);
        final long now = validationContext.getNow();
        final LWProjekt projekt = validationContext.getValidationProjekt();

        if (projekt.getProjektnummer().contains("-")) {
            String kitchenCandidateProjektnummer =
                    projekt.getProjektnummer().substring(0, projekt.getProjektnummer().indexOf("-"));

            LogiwareService service = server.locateService(LogiwareService.class);
            LWObjectCache cache = service.createObjectCache();

            LWProjekt kitchenCandidate = cache.loadSingle(LWProjekt.class,
                    Factory.eq(LWProjektMeta.ATR_PROJEKTNUMMER, kitchenCandidateProjektnummer),
                    Factory.eq(LWProjektMeta.ATR_GSE_PROJEKT, projekt.getPK().getGSE()));

            if (!(kitchenCandidate == null) && !kitchenCandidate.isBasisprojekt()) {
                for (LWAuftrag baseAuftrag : projekt.getAuftraege()) {
                    if (!isEligibleForCheck(baseAuftrag, validationContext, parameters)) {
                        continue;
                    }

                    int severity = 0;
                    for (LWAuftrag auftrag : kitchenCandidate.getAuftraege()) {
                        if (!isAuftragRelevant(validationContext, auftrag, parameters)) {
                            continue;
                        }

                        LWAuftragKopf kopf = auftrag.getAuftragKopf();
                        if (kopf == null) {
                            continue;
                        }

                        if (TimestampUtil.isNull(kopf.getBezugsDatum())) {
                            continue;
                        }

                        final int delta = TimestampUtil.numberOfCalendarDaysBetween(now, kopf.getBezugsDatum());
                        if (delta < parameters.maxDays) {
                            continue;
                        }

                        // -- determine severity ------------------------------------------------------------------------------
                        if (!liesWithinSpecifiedTimeInterval(baseAuftrag.getWunschDatum(), kopf.getBezugsDatum(), parameters)) {
                            severity = 3;
                        }
                    }
                    validationContext.addResult(this, severity, baseAuftrag);
                }
            }
        }
    }

    private boolean isEligibleForCheck(final LWAuftrag auftrag, final VMDValidationContext validationContext,
                                       Rule131.Parameters parameters) {
        if (!validationContext.isRelevant(auftrag)) {
            return false;
        }

        if (!parameters.abwicklungsArtenBaseAuftrag.contains(auftrag.getAbwicklungsart())) {
            return false;
        }

        if (TimestampUtil.isNull(auftrag.getWunschDatum())) {
            return false;
        }

        final LWAuftragKopf kopf = auftrag.getAuftragKopf();
        if (kopf == null) {
            return false;
        }

        final LWGranit2Code g2Code = kopf.getGranit2();
        if (g2Code == null) {
            return false;
        }

        if (!g2Code.isEigentumsobjekt())
            return false;

        if (isTypenhaus(auftrag)) {
            return false;
        }

        return true;
    }

    private boolean isAuftragRelevant(final VMDValidationContext validationContext, final LWAuftrag auftrag,
                                      Rule131.Parameters parameters) {
        if (!validationContext.isRelevant(auftrag))
            return false;

        if (!parameters.abwicklungsArtenRequiredAuftrag.contains(auftrag.getAbwicklungsart()))
            return false;

        return true;
    }

    private boolean liesWithinSpecifiedTimeInterval(final long wunschDatum, final long bezugsdatum,
                                                    Rule131.Parameters parameters) {
        final long startTimeInterval = bezugsdatum -
                parameters.maxNumberOfWeeksBeforeBezugsdatum * TimestampUtil.INTERVAL_ONE_WEEK;
        final long endTimeInterval = bezugsdatum -
                parameters.minNumberOfWeeksBeforeBezugsdatum * TimestampUtil.INTERVAL_ONE_WEEK;
        return startTimeInterval <= wunschDatum && wunschDatum < endTimeInterval;
    }

    // --------------------------------------------------------------
    // ---
    // --- Parameters
    // ---
    // --------------------------------------------------------------
    private final static Set<AbwicklungsartCode> ABWICKLUNGSARTEN_BASE_AUFTRAG_DEFAULT = Set.asReadonlySet(
            AbwicklungsartCode.AWA137
    );

    private final static Set<AbwicklungsartCode> ABWICKLUNGSARTEN_REQUIRED_AUFTRAG_DEFAULT = Set.asReadonlySet(
            AbwicklungsartCode.AWA001,
            AbwicklungsartCode.AWA135
    );

    static class Parameters extends VMDRuleParameters {
        public final int maxDays;
        public final int maxNumberOfWeeksBeforeBezugsdatum;
        public final int minNumberOfWeeksBeforeBezugsdatum;
        public final Set<AbwicklungsartCode> abwicklungsArtenBaseAuftrag;
        public final Set<AbwicklungsartCode> abwicklungsArtenRequiredAuftrag;

        Parameters(final VMDRuleValidator rule) {
            maxDays = rule.getIntParameter(0, 35);
            maxNumberOfWeeksBeforeBezugsdatum = rule.getIntParameter(1, 4);
            minNumberOfWeeksBeforeBezugsdatum = rule.getIntParameter(2, 3);
            abwicklungsArtenBaseAuftrag = rule.getCodeSetParameter(
                    3, AbwicklungsartCode.class, ABWICKLUNGSARTEN_BASE_AUFTRAG_DEFAULT);
            abwicklungsArtenRequiredAuftrag = rule.getCodeSetParameter(
                    4, AbwicklungsartCode.class, ABWICKLUNGSARTEN_REQUIRED_AUFTRAG_DEFAULT);
        }
    }

    @Override
    protected VMDRuleParameters getParameters() throws Exception {
        return new Rule131.Parameters(this);
    }

}
