package ch.eisenring.vmd.server.problemowner;

import java.io.IOException;
import java.io.InputStream;

import ch.eisenring.core.application.DataFile;
import ch.eisenring.core.application.DataFileParser;
import ch.eisenring.core.codetype.AbstractCode;
import ch.eisenring.core.collections.Lookup;
import ch.eisenring.core.collections.impl.ArrayLookup;
import ch.eisenring.core.csv.CSVFile;
import ch.eisenring.core.csv.CSVLine;
import ch.eisenring.core.csv.CSVOptions;
import ch.eisenring.core.csv.CSVReader;
import ch.eisenring.core.datatypes.strings.Strings;
import ch.eisenring.core.logging.Logger;
import ch.eisenring.vmd.shared.codetables.VMDProblemOwnerType;

final class VMDFixedProblemOwnerResolver {

	private final static DataFileParser<Lookup<VMDProblemOwnerType, String>> DATA_PARSER = new DataFileParser<Lookup<VMDProblemOwnerType, String>>() {
		@Override
		public Lookup<VMDProblemOwnerType, String> parseFile(
				final DataFile<Lookup<VMDProblemOwnerType, String>> dataFile) throws IOException, Exception {
			final CSVFile csvFile;
			try (final InputStream dataIn = dataFile.getInputStream()) {
				csvFile = CSVReader.readCSV(dataIn, new CSVOptions());
			}
			if (csvFile == null)
				throw new IOException(Strings.concat("Datei: \"", dataFile, "\" konnte nicht gelesen werden"));
			final int rowCount = csvFile.getLineCount();
			final Lookup<VMDProblemOwnerType, String> result = new ArrayLookup<>(rowCount);
			for (int rowIndex = 0; rowIndex < rowCount; ++rowIndex) {
				final CSVLine line = csvFile.getLine(rowIndex);
				// skip lines with too little data
				if (line.getColumnCount() < 2)
					continue;
				Integer key;
				try {
					key = Strings.parseInt(Strings.clean(line.getColumn(0)));
				} catch (final Exception e) {
					key = null;
				}
				final VMDProblemOwnerType problemOwnerType = AbstractCode.getByKey(key, VMDProblemOwnerType.class);
				// skip invalid type id's
				if (AbstractCode.isNull(problemOwnerType))
					continue;
				// skip empty users
				final String problemOwnerName = Strings.trim(line.getColumn(1));
				if (Strings.isEmpty(problemOwnerName))
					continue;
				result.put(problemOwnerType, problemOwnerName);
			}
			result.trimToSize();
			return result;
		}
	};

	private final static DataFile<Lookup<VMDProblemOwnerType, String>> DATA_FILE = new DataFile<>(
			"../data/vmd/Verantwortliche_Fixepersonen.csv", DATA_PARSER);

	static String resolve(final VMDProblemOwnerType ownerType) {
		try {
			final Lookup<VMDProblemOwnerType, String> lookup = DATA_FILE.getData();
			return lookup.get(ownerType);
		} catch (final Exception e) {
			Logger.error(e);
		}
		return null;
	}

}
