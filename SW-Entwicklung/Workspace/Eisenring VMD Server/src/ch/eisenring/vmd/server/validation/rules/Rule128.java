package ch.eisenring.vmd.server.validation.rules;

import ch.eisenring.core.collections.Set;
import ch.eisenring.core.datatypes.date.TimestampUtil;
import ch.eisenring.logiware.code.pseudo.AbwicklungsartCode;
import ch.eisenring.logiware.code.soft.LWGranit2Code;
import ch.eisenring.lw.model.navigable.LWAuftrag;
import ch.eisenring.lw.model.navigable.LWAuftragKopf;
import ch.eisenring.lw.model.navigable.LWProjekt;
import ch.eisenring.vmd.server.VMDServer;
import ch.eisenring.vmd.server.validation.VMDRuleParameters;
import ch.eisenring.vmd.server.validation.VMDRuleValidator;
import ch.eisenring.vmd.server.validation.VMDValidationContext;

/**
 * Implements rule #128: "Bezugsdatum Miet nicht eingetragen"
 */
public class Rule128 extends VMDRuleValidator {

    public Rule128(final VMDServer server, final int ruleId) {
        super(server, ruleId);
    }

    @Override
    public void validateImpl(final VMDValidationContext validationContext) throws Exception {
        final Rule128.Parameters parameters = (Rule128.Parameters) validationContext.getParameters(this);
        final long now = validationContext.getNow();
        final LWProjekt projekt = validationContext.getValidationProjekt();

        for (LWAuftrag auftrag : projekt.getAuftraege()) {
            int severity = 0;
            do {
                if (!validationContext.isRelevant(auftrag)) {
                    break;
                }

                if (!parameters.abwicklungsArten.contains(auftrag.getAbwicklungsart()))
                    break;

                if (TimestampUtil.isNull(auftrag.getWunschDatum())) {
                    break;
                }

                final LWAuftragKopf kopf = auftrag.getAuftragKopf();
                if (kopf == null) {
                    break;
                }

                final LWGranit2Code g2Code = kopf.getGranit2();
                if (g2Code == null) {
                    break;
                }

                if (!g2Code.isMietobjekt())
                    break;

                final int delta = TimestampUtil.numberOfCalendarDaysBetween(now, auftrag.getWunschDatum());
                if (delta < parameters.maxDays && TimestampUtil.isNull(kopf.getBezugsDatum())) {
                    severity = 3;
                }
            } while (false);
            validationContext.addResult(this, severity, auftrag);
        }
    }

    // --------------------------------------------------------------
    // ---
    // --- Parameters
    // ---
    // --------------------------------------------------------------
    private final static Set<AbwicklungsartCode> ABWICKLUNGSARTEN_DEFAULT = Set.asReadonlySet(
            AbwicklungsartCode.AWA001,
            AbwicklungsartCode.AWA135
    );

    static class Parameters extends VMDRuleParameters {
        public final int maxDays;
        public final Set<AbwicklungsartCode> abwicklungsArten;

        Parameters(final VMDRuleValidator rule) {
            maxDays = rule.getIntParameter(0, 40);
            abwicklungsArten = rule.getCodeSetParameter(1, AbwicklungsartCode.class, ABWICKLUNGSARTEN_DEFAULT);
        }
    }

    @Override
    protected VMDRuleParameters getParameters() throws Exception {
        return new Rule128.Parameters(this);
    }
}
