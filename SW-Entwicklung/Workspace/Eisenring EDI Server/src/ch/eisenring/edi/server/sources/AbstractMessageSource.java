package ch.eisenring.edi.server.sources;

import java.util.Date;
import javax.mail.Address;
import ch.eisenring.edi.MessageSource;

public abstract class AbstractMessageSource implements MessageSource {

	private Address[] originator;

	@Override
	public void setEmailOriginator(Address[] originator) {
		this.originator = originator;
	}

	@Override
	public Address[] getEmailOriginator() {
		return originator;
	}

	private Address[] recipient;

	@Override
	public void setEmailRecipient(Address[] recipient) {
		this.recipient = recipient;
	}

	@Override
	public Address[] getEmailRecipient() {
		return recipient;
	}

	private java.util.Date timestamp;

	@Override
	public void setEmailTimeStamp(Date timestamp) {
		this.timestamp = timestamp;
	}

	@Override
	public Date getEmailTimeStamp() {
		return timestamp;
	}

	private String subject;

	@Override
	public void setEmailSubject(String subject) {
		this.subject = subject;
	}

	@Override
	public String getEmailSubject() {
		return subject;
	}

}
