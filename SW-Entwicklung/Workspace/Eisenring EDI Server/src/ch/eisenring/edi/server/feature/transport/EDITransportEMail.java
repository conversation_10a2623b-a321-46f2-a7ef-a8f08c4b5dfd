package ch.eisenring.edi.server.feature.transport;

import ch.eisenring.app.shared.ServiceLocator;
import ch.eisenring.core.codetable.MessageClassCode;
import ch.eisenring.core.codetable.Protocol;
import ch.eisenring.core.datatypes.primitives.Primitives;
import ch.eisenring.core.datatypes.strings.Strings;
import ch.eisenring.core.logging.Logger;
import ch.eisenring.edi.Message;
import ch.eisenring.edi.server.EDIHelper;
import ch.eisenring.edi.server.config.EDIConfiguration;
import ch.eisenring.edi.server.feature.EDIContext;
import ch.eisenring.email.EMail;
import ch.eisenring.email.EMailAddress;
import ch.eisenring.email.EMailTemplate;
import ch.eisenring.user.shared.service.USRService;
import ch.eisenring.user.shared.service.UserFacade;

/**
 * Implements EDI outgoing transport by email (with check of EDI contents)
 */
public class EDITransportEMail extends EDITransportEMailBase {

	@Override
	public String getFeatureName() {
		return "EMail";
	}

	@Override
	public String getTargetIndicator(final EDIContext context) {
		final String className = Primitives.getSimpleName(getClass());
		String target;
		try {
			target = context.configuration.getString(CKEY_TOOK, null);
			if (Strings.isEmpty(target))
				target = "<empty>";
		} catch (final Exception e) {
			target = "<error determining target>";
		}
		return TARGET_INDICATOR_MSG.build(className, target);
	}

	@Override
	public void transport(final EDIContext context) {
		// determine SaBe mail address
		final boolean addSaBeCCFromEDI = context.configuration.getBoolean(CKEY_ADD_SABE_CC_FROM_EDI, true);
		if (addSaBeCCFromEDI) {
			getSaBeEmail(context);
		}

		// create send email with processing result
		checkConfiguration(context.configuration);
		final EMailTemplate template = getTemplate(context.getProtocol(), context.configuration);
		template.addModel(context);
		final EMail mail = template.toEMail();
		setSender(context, mail);
		final EMailAddress email = addSaBeCCFromEDI
				? (EMailAddress) context.getData(EDIContext.KEY_EMAILADDRESS2)
				: null;

		if (!context.getProtocol().isSuccess()) {
			// error case
			String additional;
			try {
				attachOriginalEDI(context, mail);
				additional = "Original EANCOM Nachricht im Anhang";
			} catch (final Exception e) {
				additional = Strings.concat("Original EANCOM Nachricht nicht verfügbar: ", e.getMessage());
			}
			mail.setSubject("EANCOM Verarbeitungsfehler");
			appendProtocol(context, mail, additional);
			mail.addTo(context.configuration.getString(CKEY_TOERROR, null));
			if (email != null)
				mail.addTo(email);
		} else {
			// ok case
			appendProtocol(context, mail, null);
			attachOutputData(context, mail);
			mail.addTo(context.configuration.getString(CKEY_TOOK, null));
			mail.addCc(email);
		}

		// attempt to send the mail
		if (sendMail(context.server, mail)) {
			try {
				// delete the EDI source file after sending sucessfully
				context.markMessageSourceForDeletion();
			} catch (final Exception e) {
				Logger.error(e);
				context.addProtocolError("Mailversand fehlgeschlagen", e);
			}
		}
	}

	/**
	 * Determine SaBe email based on EDI message
	 */
	private static void getSaBeEmail(final EDIContext context) {
		final String action = "Sachbearbeiter EMail-Adresse ermitteln";
		final Message message;
		try {
			message = context.getMessage();
		} catch (final Exception e) {
			context.addProtocolError(action, e.getMessage());
			return;
		}
	
		String emailSaBe = null;
		String nameSaBe = null;
		try {
			do {
				// Kurzzeichen SaBe ermitteln
				final String sabeShort = EDIHelper.getSaBeShort(message);
				if (sabeShort == null) {
					//context.addProtocol(MessageClassCode.INFO, action, "Kurzzeichen SaBe nicht aus EANCOM ermittelbar, Segment CTA+OC+ überprüfen");
					//break;
				}
				// Kurzzeichen zu Benutzer auflösen
				final UserFacade user = getUser(context.server, sabeShort);
				if (user == null) {
					//context.addProtocol(MessageClassCode.INFO, action, "Kein Benutzer mit Kurzzeichen \"" + sabeShort + "\" im System gefunden");
					//break;
				}
				if (user != null) {
					nameSaBe = user.getFullname();
					emailSaBe = user.getEMailAddress();
				}

				if (Strings.isEmpty(emailSaBe)) {
					context.addProtocol(MessageClassCode.INFO, action, "Für \"" + sabeShort + "\" konnte keine EMail ermittelt werden.");
					final EMailAddress address = new EMailAddress("<EMAIL>", "IT Helpdesk");
					context.putData(EDIContext.KEY_EMAILADDRESS2, address);
				} else {
					// put mail address
					final EMailAddress address = new EMailAddress(emailSaBe, nameSaBe);
					context.putData(EDIContext.KEY_EMAILADDRESS2, address);
					context.addProtocolOk(action, nameSaBe + ", " + emailSaBe);
				}
			} while (false);
		} catch (final Exception e) {
			context.addProtocolError(action, e.getMessage());
		}
	}	

	/**
	 * Determine user by short name
	 */
	public static UserFacade getUser(final ServiceLocator locator, final String shortName) {
		if (Strings.isEmpty(shortName))
			return null;
		try {
			final USRService service = locator.locateService(USRService.class);
			final UserFacade user = service.getUser(shortName);
			if (user != null)
				return user;
		} catch (final Exception e) {
			// abort
		}
		return null;
	}

	// --------------------------------------------------------------
	// ---
	// --- Configuration validation
	// ---
	// --------------------------------------------------------------
	@Override
	public Protocol checkConfiguration(final EDIConfiguration configuration) {
		return super.checkConfiguration(configuration);
	}

}
