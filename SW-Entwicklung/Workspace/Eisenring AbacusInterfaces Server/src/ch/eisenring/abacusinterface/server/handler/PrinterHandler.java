package ch.eisenring.abacusinterface.server.handler;

import java.awt.Dimension;
import java.awt.print.*;
import java.util.TreeMap;
import javax.print.PrintService;
import javax.print.PrintServiceLookup;
import ch.eisenring.abacusinterface.server.handler.PrinterPageFormat.PageOrientation;
import ch.eisenring.core.logging.Logger;

public class PrinterHandler {

	private static final java.util.Map<String, PrintService> PRINTSERVICEMAP = new TreeMap<>();

	public static PrintService[] getPrintServices() {
		return PrintServiceLookup.lookupPrintServices(null, null);
	}

	public static PrintService getDefaultPrintService() {
		return PrintServiceLookup.lookupDefaultPrintService();
	}

	public static PrintService getPrintService(String printServiceName) {
		Logger.debug("NSW Batch PrintService: Suche PrintService: " + printServiceName);
		try {
			PrintService[] printServices = getPrintServices();
			if (printServices != null) {
				for (PrintService printService : printServices) {
					Logger.debug("NSW Batch PrintService: Installierter PrintService " + printService);
					PRINTSERVICEMAP.put(new java.io.File(printService.getName()).getName(), printService);
				}
			}
		} catch (Exception e) {
			ch.eisenring.core.logging.Logger.error(e);
		}
		return PRINTSERVICEMAP.get(new java.io.File(printServiceName).getName());
	}

	/**
	 * Print out one PDF.
	 *
	 * @param printService the shortname of the Printer or <code>null</code> for Default Printer.
	 * @param pdf          the PDF to print
	 * @throws PrinterException
	 */
	public static synchronized void printPdf(PrintService printService, org.faceless.pdf2.PDF pdf, String jobName) throws PrinterException {
		java.awt.print.PrinterJob printerJob = java.awt.print.PrinterJob.getPrinterJob();
		String printJobName = "NSW-Batch_" + jobName + "_" + System.currentTimeMillis();
		printerJob.setJobName(printJobName);
		if (printService != null) {
			Logger.debug("Abacus Batch NSW: Printauftrag mit Name " + printJobName + " aufgegeben auf Printer " + printService.getName());
			printerJob.setPrintService(printService);
		} else {
			Logger.debug("Abacus Batch NSW: Printauftrag mit Name " + printJobName + " aufgegeben ohne PrinteService");
			PrintService fallBack = printerJob.getPrintService();
			if (fallBack != null) {
				Logger.debug("Abacus Batch NSW: Printauftrag mit Name " + printJobName + " aufgegeben mit Fallback PrintService: " + fallBack.getName());
			}
		}
		final org.faceless.pdf2.PDFParser parser = new org.faceless.pdf2.PDFParser(pdf);
		printerJob.setPageable(new java.awt.print.Pageable() {
			@Override
			public int getNumberOfPages() {
				return parser.getNumberOfPages();
			}

			@Override
			public PageFormat getPageFormat(int pageIndex) throws IndexOutOfBoundsException {
				Printable printable = parser.getPrintable(pageIndex);
				org.faceless.pdf2.PagePainter pagePainter = printable instanceof org.faceless.pdf2.PagePainter
					? (org.faceless.pdf2.PagePainter) printable
					: parser.getPagePainter(pageIndex);
				org.faceless.pdf2.PDFPage page = pagePainter.getPage();
				Dimension dim = new Dimension(page.getWidth(), page.getHeight());
				PageFormat pageFormat = new java.awt.print.PageFormat();
				Paper paper = printerJob.defaultPage().getPaper();
				paper.setImageableArea(0, 0, paper.getWidth(), paper.getHeight());
				pageFormat.setPaper(paper);
				pageFormat.setOrientation(PageOrientation.get(dim).getAsInt());
				return pageFormat;
			}

			@Override
			public Printable getPrintable(int pageIndex) throws IndexOutOfBoundsException {
				return parser.getPrintable(pageIndex);
			}
		});
		printerJob.print();
	}

}
